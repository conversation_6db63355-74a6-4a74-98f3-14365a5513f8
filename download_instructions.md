# TroubleShooting

This document provides instructions for downloading the required files for the Amazon Q Language Server assets based on your operating system and architecture.

## Identifying Your Platform

First, determine your operating system and CPU architecture:

| Operating System | Architecture | Platform Identifier |
|------------------|--------------|---------------------|
| Windows          | x64 (64-bit) | windows-x64         |
| Windows          | ARM64        | windows-arm64       |
| Linux            | x64 (64-bit) | linux-x64           |
| Linux            | ARM64        | linux-arm64         |
| macOS            | x64 (Intel)  | darwin-x64          |
| macOS            | ARM64 (M1/M2)| darwin-arm64        |

## Downloading Required Files

For each platform, you need to download two files:

1. **servers.zip** - Contains the language server server-side components specific to your platform
2. **clients.zip** - Contains the language server client-side components (same for all platforms)

### Steps to Download

1. **Check the manifest file** https://aws-toolkit-language-servers.amazonaws.com/qAgenticChatServer/0/manifest.json for the latest version information
    - Look for the first entry in the "versions" array that is marked as "isDelisted": false
    - Note the "serverVersion" value as this is the latest version

2. **Locate your platform in the "targets" section** of the latest version
    - Find the entry that matches your operating system and architecture

3. **Download both required files**
    - For each file in the "contents" section:
        - Download the file from the URL specified in the "url" field
        - Note the file size indicated in the "bytes" field

## Verifying File Integrity

After downloading, verify the integrity of the files using the SHA384 hash provided in the manifest:

### On Windows (PowerShell):
```powershell
Get-FileHash -Path path\to\downloaded\file.zip -Algorithm SHA384 | Format-List
```

### On macOS/Linux:
```bash
shasum -a 384 path/to/downloaded/file.zip
```

Compare the output hash with the SHA384 hash provided in the "hashes" array for each file in the manifest.

## Installation
To manually add Amazon Q language server assets locally, copy the downloaded servers.zip and clients.zip to the specific file system locations below:
For JetBrains:
```
Mac: /Users/<USER>/Library/Caches/aws/toolkits/language-servers/AmazonQ-JetBrains-temp
Windows: C:\Users\<USER>\AppData\Local\aws\toolkits\language-servers\AmazonQ-JetBrains-temp
Linux: /home/<USER>/.cache/aws/toolkits/language-servers/AmazonQ-JetBrains-temp
```
For Visual Studio Code:
```
Mac: /Users/<USER>/Library/Caches/aws/toolkits/language-servers/AmazonQ
Windows: C:\Users\<USER>\AppData\Local\aws\toolkits\language-servers\AmazonQ
Linux: /home/<USER>/.cache/aws/toolkits/language-servers/AmazonQ
```

## Third-Party Licenses

For information about third-party licenses, refer to the URL specified in the "thirdPartyLicenses" field of the manifest for the version you downloaded.
