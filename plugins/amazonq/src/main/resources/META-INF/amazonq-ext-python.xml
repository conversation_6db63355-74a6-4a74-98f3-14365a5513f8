<!-- Copyright 2024 Amazon.com, Inc. or its affiliates. All Rights Reserved. -->
<!-- SPDX-License-Identifier: Apache-2.0 -->

<idea-plugin>
    <extensions defaultExtensionNs="amazon.q.codewhisperer">
        <programmingLanguage implementation="software.aws.toolkits.jetbrains.services.codewhisperer.language.languages.CodeWhispererPython"/>
        <classResolver implementation="software.aws.toolkits.jetbrains.services.codewhisperer.language.classresolver.CodeWhispererPythonClassResolver"/>
        <importAdder implementation="software.aws.toolkits.jetbrains.services.codewhisperer.importadder.CodeWhispererPythonImportAdder"/>
    </extensions>

    <extensions defaultExtensionNs="software.aws.toolkits.jetbrains">
        <moduleDependencyProvider
            implementation="software.aws.toolkits.jetbrains.services.amazonq.lsp.dependencies.providers.PythonModuleDependencyProvider"/>
    </extensions>
</idea-plugin>
