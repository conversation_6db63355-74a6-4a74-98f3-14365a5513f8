<!-- Copyright 2024 Amazon.com, Inc. or its affiliates. All Rights Reserved. -->
<!-- SPDX-License-Identifier: Apache-2.0 -->

<idea-plugin xmlns:xi="http://www.w3.org/2001/XInclude" require-restart="true">
    <id>amazon.q</id>
    <name>Amazon Q</name>
    <description><![CDATA[
    <p>The most capable generative AI-powered assistant for building, operating, and transforming software, with advanced capabilities for managing data and AI</p>

    <h2>Agent capabilities</h2>
    <h3>Implement new features</h3>
    <p><code>/dev</code> to task Amazon Q with generating new code across your entire project and implement features.</p>

    <h3>Generate documentation</h3>
    <p><code>/doc</code> to task Amazon Q with writing API, technical design, and onboarding documentation.</p>

    <h3>Automate code reviews</h3>
    <p><code>/review</code> to ask Amazon Q to perform code reviews, flagging suspicious code patterns and assessing deployment risk.</p>

    <h3>Generate unit tests</h3>
    <p><code>/test</code> to ask Amazon Q to generate unit tests and add them to your project, helping you improve code quality, fast.</p>

    <h3>Transform workloads</h3>
    <p><code>/transform</code> to upgrade your Java applications in minutes, not weeks.</p>

    <h2>Core features</h2>

    <h3>Inline chat</h3>
    <p>Seamlessly initial chat within the inline coding experience. Select a section of code that you need assistance with and initiate chat within the editor to request actions such as "Optimize this code", "Add comments", or "Write tests".</p>

    <h3>Chat</h3>
    <p>Generate code, explain code, and get answers about software development.</p>

    <h3>Inline suggestions</h3>
    <p><Receive real-time code suggestions ranging from snippets to full functions based on your comments and existing code.</p>
    <p><i><a href="https://docs.aws.amazon.com/amazonq/latest/qdeveloper-ug/q-language-ide-support.html">15+ languages supported including Python, TypeScript, Rust, Terraform, AWS Cloudformation, and more</a></i></p>

    <h3>Code reference log</h3>
    <p>Attribute code from Amazon Q that is similar to training data. When code suggestions similar to training data are accepted, they will be added to the code reference log.</p>

    <h1>Getting Started</h1>
    <p><b>Free Tier</b> - create or log in with an AWS Builder ID (a personal profile from AWS).</p>
    <p><b>Pro Tier</b> - if your organization is on the Amazon Q Developer Pro tier, log in with single sign-on.</p>

    <img src="https://raw.githubusercontent.com/aws/aws-toolkit-jetbrains/main/marketplaceGifs/JB-auth-Q.gif"></img>

    <h3>Troubleshooting & feedback</h3>
    <p><a href="https://github.com/aws/aws-toolkit-jetbrains/issues/new?labels=bug&template=bug_report.md">File a bug</a> or <a href="https://github.com/aws/aws-toolkit-jetbrains/issues/new?labels=feature-request&template=feature_request.md">submit a feature request</a> on our Github repository.</p>

]]></description>
    <version>1.0</version>
    <vendor email="<EMAIL>" url="https://github.com/aws/aws-toolkit-jetbrains">AWS</vendor>
    <idea-version since-build="232" />
    <depends>com.intellij.modules.platform</depends>
    <depends optional="true">org.jetbrains.idea.maven</depends>
    <resource-bundle>software.aws.toolkits.resources.MessagesBundle</resource-bundle>

    <depends>aws.toolkit.core</depends>
    <depends>com.intellij.modules.lang</depends>

    <depends optional="true" config-file="amazonq-ext-java.xml">com.intellij.java</depends>
    <depends optional="true" config-file="amazonq-ext-nodejs.xml">JavaScriptDebugger</depends>
    <depends optional="true" config-file="amazonq-ext-python.xml">com.intellij.modules.python</depends>

    <incompatible-with>com.intellij.cwm.guest</incompatible-with>
    <incompatible-with>com.intellij.jetbrains.client</incompatible-with>
    <incompatible-with>com.intellij.gateway</incompatible-with>

    <extensions defaultExtensionNs="com.intellij">
        <registryKey key="amazon.q.endpoint" description="Endpoint to use for Amazon Q"
                     defaultValue="" restartRequired="true"/>
        <registryKey key="amazon.q.endpoints.json" description="List of region-endpoint pairs in JSON array form"
                     defaultValue=""
                     restartRequired="true"/>
        <registryKey key="inline.completion.rem.dev.use.rhizome" description="Defined by IntelliJ. Used for Amazon Q to display suggestions on remote."
                     defaultValue="false" restartRequired="true"/>
    </extensions>

    <extensionPoints>
        <extensionPoint qualifiedName="software.aws.toolkits.jetbrains.moduleDependencyProvider"
                        interface="software.aws.toolkits.jetbrains.services.amazonq.lsp.dependencies.ModuleDependencyProvider"
                        dynamic="true"/>
    </extensionPoints>

    <xi:include href="/META-INF/module-amazonq.xml" />

    <xi:include href="/META-INF/change-notes.xml" xpointer="xpointer(/idea-plugin/*)">
        <xi:fallback/>
    </xi:include>
</idea-plugin>
