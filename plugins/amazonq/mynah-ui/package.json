{"name": "mynah-ui", "version": "1.0.0", "description": "", "main": "webpack.media.config.js", "scripts": {"webpack-media": "webpack --config webpack.media.config.js --mode development", "webpack-media-watch": "webpack --config webpack.media.config.js --mode development --watch", "test": "echo \"Error: no test specified\" && exit 0", "build-ui": "npm install && npm run webpack-media", "lint": "eslint -c .eslintrc.js --ext .ts .", "lintfix": "eslint -c .eslintrc.js --fix --ext .ts ."}, "dependencies": {"@aws/mynah-ui-chat": "npm:@aws/mynah-ui@4.30.3", "@types/node": "^14.18.5", "fs-extra": "^10.0.1", "sanitize-html": "^2.12.1", "ts-node": "^10.7.0", "uuid": "^8.3.2", "web-tree-sitter": "^0.20.7"}, "devDependencies": {"@aws/fully-qualified-names": "^2.1.1", "@aws/chat-client": "^0.1.4", "@types/sanitize-html": "^2.8.0", "@typescript-eslint/eslint-plugin": "^5.38.0", "@typescript-eslint/parser": "^5.38.0", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.6.0", "eslint": "^8.26.0", "eslint-config-prettier": "8.3", "eslint-plugin-header": "^3.1.1", "eslint-plugin-no-null": "^1.0.2", "sass": "^1.49.8", "sass-loader": "^12.6.0", "style-loader": "^3.3.1", "ts-loader": "^9.2.6", "typescript": "^4.5.2", "webpack": "^5.94.0", "webpack-cli": "^4.7.2"}, "author": "", "license": "ISC", "prettier": {"printWidth": 120, "trailingComma": "es5", "tabWidth": 4, "singleQuote": true, "semi": false, "bracketSpacing": true, "arrowParens": "avoid", "endOfLine": "lf"}}