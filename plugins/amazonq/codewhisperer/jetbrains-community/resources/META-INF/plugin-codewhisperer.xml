<!-- Copyright 2024 Amazon.com, Inc. or its affiliates. All Rights Reserved. -->
<!-- SPDX-License-Identifier: Apache-2.0 -->

<idea-plugin>
    <applicationListeners>
        <listener class="software.aws.toolkits.jetbrains.services.codewhisperer.popup.CodeWhispererUIChangeListener"
                  topic="software.aws.toolkits.jetbrains.services.codewhisperer.popup.CodeWhispererPopupStateChangeListener"/>
        <listener class="software.aws.toolkits.jetbrains.services.codewhisperer.popup.CodeWhispererUIChangeListenerNew"
                  topic="software.aws.toolkits.jetbrains.services.codewhisperer.popup.CodeWhispererPopupStateChangeListener"/>
        <listener class="software.aws.toolkits.jetbrains.services.codewhisperer.toolwindow.CodeWhispererCodeReferenceActionListener"
                  topic="software.aws.toolkits.jetbrains.services.codewhisperer.popup.CodeWhispererUserActionListener"/>
        <listener class="software.aws.toolkits.jetbrains.services.codewhisperer.toolwindow.CodeWhispererCodeReferenceActionListenerNew"
                  topic="software.aws.toolkits.jetbrains.services.codewhisperer.popup.CodeWhispererUserActionListener"/>
    </applicationListeners>

    <projectListeners>
        <listener class="software.aws.toolkits.jetbrains.services.codewhisperer.startup.CodeWhispererProjectStartupSettingsListener"
                  topic="com.intellij.openapi.wm.ex.ToolWindowManagerListener"/>
        <listener class="software.aws.toolkits.jetbrains.services.codewhisperer.startup.CodeWhispererProjectStartupSettingsListener"
                  topic="software.aws.toolkits.jetbrains.core.credentials.ToolkitConnectionManagerListener"/>
        <listener class="software.aws.toolkits.jetbrains.services.codewhisperer.startup.CodeWhispererProjectStartupSettingsListener"
                  topic="software.aws.toolkits.jetbrains.core.credentials.sso.bearer.BearerTokenProviderListener"/>
    </projectListeners>

    <extensionPoints>
        <extensionPoint qualifiedName="amazon.q.codewhisperer.programmingLanguage" interface="software.aws.toolkits.jetbrains.services.codewhisperer.language.CodeWhispererProgrammingLanguage" dynamic="true"/>
        <extensionPoint qualifiedName="amazon.q.codewhisperer.classResolver" interface="software.aws.toolkits.jetbrains.services.codewhisperer.language.classresolver.CodeWhispererClassResolver" dynamic="true"/>
        <extensionPoint qualifiedName="amazon.q.codewhisperer.importAdder" interface="software.aws.toolkits.jetbrains.services.codewhisperer.importadder.CodeWhispererImportAdder" dynamic="true"/>
    </extensionPoints>

    <extensions defaultExtensionNs="com.intellij">
        <applicationService serviceInterface="migration.software.aws.toolkits.jetbrains.services.codewhisperer.customization.CodeWhispererModelConfigurator"
                            serviceImplementation="software.aws.toolkits.jetbrains.services.codewhisperer.customization.DefaultCodeWhispererModelConfigurator"/>

        <applicationService serviceImplementation="software.aws.toolkits.jetbrains.services.amazonq.profile.QRegionProfileManager"/>
        <projectService serviceInterface="software.aws.toolkits.jetbrains.services.codewhisperer.credentials.CodeWhispererClientAdaptor"
                        serviceImplementation="software.aws.toolkits.jetbrains.services.codewhisperer.credentials.CodeWhispererClientAdaptorImpl"/>
        <projectService serviceInterface="software.aws.toolkits.jetbrains.services.codewhisperer.util.FileContextProvider"
                        serviceImplementation="software.aws.toolkits.jetbrains.services.codewhisperer.util.DefaultCodeWhispererFileContextProvider"/>
        <projectService serviceImplementation="software.aws.toolkits.jetbrains.services.codewhisperer.codescan.CodeWhispererCodeScanManager"/>
        <projectService serviceImplementation="software.aws.toolkits.jetbrains.services.codewhisperer.codetest.CodeWhispererUTGChatManager"/>

        <statusBarWidgetFactory id="aws.codewhisperer"
                                implementation="software.aws.toolkits.jetbrains.services.codewhisperer.status.CodeWhispererStatusBarWidgetFactory"/>

        <postStartupActivity implementation="software.aws.toolkits.jetbrains.services.codewhisperer.startup.CodeWhispererProjectStartupActivity"/>

        <toolWindow id="aws.codewhisperer.codereference" anchor="bottom" doNotActivateOnStart="true" canCloseContents="true"
                    factoryClass="software.aws.toolkits.jetbrains.services.codewhisperer.toolwindow.CodeWhispererCodeReferenceToolWindowFactory"
                    icon="AllIcons.Actions.Preview"/>

        <projectConfigurable
            parentId="aws"
            id="aws.codewhisperer"
            bundle="software.aws.toolkits.resources.MessagesBundle"
            key="aws.settings.codewhisperer.configurable.title"
            instance="software.aws.toolkits.jetbrains.services.codewhisperer.settings.CodeWhispererConfigurable"
        />

        <typedHandler implementation="software.aws.toolkits.jetbrains.services.codewhisperer.editor.CodeWhispererTypedHandler"/>
        <editorActionHandler action="EditorEnter" implementationClass="software.aws.toolkits.jetbrains.services.codewhisperer.editor.CodeWhispererEnterHandler"
                             order="first, before editorEnter"/>
        <actionPromoter order="last" implementation="software.aws.toolkits.jetbrains.services.codewhisperer.actions.CodeWhispererActionPromoter"/>
        <fileEditorProvider implementation="software.aws.toolkits.jetbrains.services.codewhisperer.learn.LearnCodeWhispererEditorProvider"/>

        <!-- Inline Completion Provider -->
        <inline.completion.provider id="amazon.q" implementation="software.aws.toolkits.jetbrains.services.codewhisperer.popup.QInlineCompletionProvider"/>
    </extensions>

    <extensions defaultExtensionNs="aws.toolkit.core">
        <sdk.clientCustomizer implementation="software.aws.toolkits.jetbrains.services.codewhisperer.util.CodeWhispererEndpointCustomizer"/>
    </extensions>

    <extensions defaultExtensionNs="amazon.q.codewhisperer">
        <!-- TODO: c/c++ extensions should live its own module instead of jetbrains-core -->
        <programmingLanguage implementation="software.aws.toolkits.jetbrains.services.codewhisperer.language.languages.CodeWhispererC"/>
        <programmingLanguage implementation="software.aws.toolkits.jetbrains.services.codewhisperer.language.languages.CodeWhispererCpp"/>
        <importAdder implementation="software.aws.toolkits.jetbrains.services.codewhisperer.importadder.CodeWhispererFallbackImportAdder"/>
    </extensions>

    <actions resource-bundle="software.aws.toolkits.resources.AmazonQBundle">
        <group id="aws.toolkit.explorer.codewhisperer" popup="true" compact="true">
            <action id="codewhisperer.settings"
                    class="software.aws.toolkits.jetbrains.services.codewhisperer.actions.CodeWhispererShowSettingsAction"/>
            <action id="codewhisperer.switchProfiles"
                    class="software.aws.toolkits.jetbrains.services.amazonq.actions.QSwitchProfilesAction"/>
            <separator/>
            <action id="codewhisperer.whatIs"
                    class="software.aws.toolkits.jetbrains.services.codewhisperer.actions.CodeWhispererWhatIsAction"/>
        </group>

        <action class="software.aws.toolkits.jetbrains.services.codewhisperer.actions.CodeWhispererRecommendationAction"
                text="Invoke Amazon Q Inline Suggestions">
            <keyboard-shortcut keymap="$default" first-keystroke="alt C"/>
        </action>
        <action id="codewhisperer.inline.navigate.previous"
                class="software.aws.toolkits.jetbrains.services.codewhisperer.actions.CodeWhispererNavigatePrevAction"
                text="Navigate to Previous Inline Suggestion" description="Navigate to previous inline suggestion">
            <keyboard-shortcut keymap="$default" first-keystroke="alt OPEN_BRACKET"/>
        </action>
        <action id="codewhisperer.inline.navigate.next"
                class="software.aws.toolkits.jetbrains.services.codewhisperer.actions.CodeWhispererNavigateNextAction"
                text="Navigate to Next Inline Suggestion" description="Navigate to next inline suggestion">
            <keyboard-shortcut keymap="$default" first-keystroke="alt CLOSE_BRACKET"/>
        </action>
        <action id="codewhisperer.inline.accept"
                class="software.aws.toolkits.jetbrains.services.codewhisperer.actions.CodeWhispererAcceptAction"
                text="Accept the Current Inline Suggestion" description="Accept the current inline suggestions">
            <keyboard-shortcut keymap="$default" first-keystroke="TAB"/>
        </action>
        <action id="codewhisperer.inline.force.accept"
                class="software.aws.toolkits.jetbrains.services.codewhisperer.actions.CodeWhispererForceAcceptAction"
                text="Force Accept the Current Amazon Q Suggestion" description="Force accept the current Amazon Q suggestion">
            <keyboard-shortcut keymap="Mac OS X" first-keystroke="alt TAB"/>
            <keyboard-shortcut keymap="Mac OS X 10.5+" first-keystroke="alt TAB"/>
            <keyboard-shortcut keymap="$default" first-keystroke="alt ENTER"/>
        </action>


        <group id="aws.toolkit.codewhisperer.toolbar.security">
            <group id="codewhisperer.toolbar.security.group" icon="AllIcons.Actions.GroupBy" text="Group" popup="true">
                <separator text="Group By"/>
                <group id="CodeWhispererCodeScanGroupBy"
                       class="software.aws.toolkits.jetbrains.services.codewhisperer.codescan.actions.CodeWhispererCodeScanGroupingStrategyActionGroup" text="Group"/>
            </group>
            <group id="codewhisperer.toolbar.security.filter" icon="AllIcons.General.Filter" text="Filter" popup="true">
                <separator text="Severity"/>
                <group id="CodeWhispererCodeScanFilterGroup"
                       class="software.aws.toolkits.jetbrains.services.codewhisperer.codescan.actions.CodeWhispererCodeScanFilterGroup" text="Severity"/>
            </group>
        </group>

        <group id="aws.toolkit.jetbrains.core.services.codewhisperer.suggestions"
               class="software.aws.toolkits.jetbrains.services.codewhisperer.explorer.QStatusBarLoggedInActionGroup" />

        <group id = "aws.toolkit.explorer.codewhisperer.codescan">
            <action
                id="aws.amazonq.explainCodeScanIssue"
                class="software.aws.toolkits.jetbrains.services.cwc.commands.codescan.actions.ExplainCodeIssueAction"/>
            <action
                id="aws.amazonq.codeScanComplete"
                class="software.aws.toolkits.jetbrains.services.cwc.commands.codescan.actions.CodeScanCompleteAction" />
        </group>

    </actions>
</idea-plugin>
