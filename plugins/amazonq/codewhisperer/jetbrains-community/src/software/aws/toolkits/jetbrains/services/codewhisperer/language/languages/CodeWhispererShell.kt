// Copyright 2023 Amazon.com, Inc. or its affiliates. All Rights Reserved.
// SPDX-License-Identifier: Apache-2.0

package software.aws.toolkits.jetbrains.services.codewhisperer.language.languages

import software.aws.toolkits.jetbrains.services.codewhisperer.language.CodeWhispererProgrammingLanguage
import software.aws.toolkits.telemetry.CodewhispererLanguage

class CodeWhispererShell private constructor() : CodeWhispererProgrammingLanguage() {
    override val languageId: String = ID

    override fun toTelemetryType(): CodewhispererLanguage = CodewhispererLanguage.Shell

    override fun lineCommentPrefix(): String = "#"

    override fun blockCommentPrefix(): String = ": '"

    override fun blockCommentSuffix(): String = "'"

    companion object {
        const val ID = "shell"

        val INSTANCE = CodeWhispererShell()
    }
}
