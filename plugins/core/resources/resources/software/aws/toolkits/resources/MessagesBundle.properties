# Copyright 2020 Amazon.com, Inc. or its affiliates. All Rights Reserved.
# SPDX-License-Identifier: Apache-2.0
action.apprunner.service.copyServiceUri.text=Copy Service URL
action.apprunner.service.openServiceUri.text=Open Service URL
action.aws.caws.devtools.actions.clone.text=Clone Repository
action.aws.caws.devtools.actions.copyCloneUrl.text=Copy Clone URL for Command Line
action.aws.caws.devtools.actions.learnMore.text=Learn More about CodeCatalyst
action.aws.caws.devtools.actions.learnMoreReAuth.text=Learn More about CodeCatalyst
action.aws.caws.devtools.actions.login.text=Start
action.aws.caws.devtools.actions.openGateway.text=Open Dev Environments in JetBrains Gateway
action.aws.caws.devtools.actions.reauthenticate.text=Re-authenticate to connect
action.aws.caws.rebuildAction.text=Rebuild Dev Environment
action.aws.caws.updateDevfile.text=Update Devfile...
action.aws.toolkit.caws.logout.text=Sign out
action.aws.toolkit.caws.profile.text=View Profile...
action.aws.toolkit.dynamodb.delete_table.text=Delete Table...
action.aws.toolkit.ecr.repository.pull.text=Pull from Repository...
action.aws.toolkit.ecr.repository.push.text=Push to Repository...
action.aws.toolkit.jetbrains.core.services.cwc.commands.ExplainCodeAction.description=Explains the selected code
action.aws.toolkit.jetbrains.core.services.cwc.commands.ExplainCodeAction.text=Explain Code
action.aws.toolkit.jetbrains.core.services.cwc.commands.FixCodeAction.description=Fixes the selected code
action.aws.toolkit.jetbrains.core.services.cwc.commands.FixCodeAction.text=Fix Code
action.aws.toolkit.jetbrains.core.services.cwc.commands.GenerateUnitTestsAction.description=Generates unit tests for the selected code
action.aws.toolkit.jetbrains.core.services.cwc.commands.GenerateUnitTestsAction.text=Generate Tests
action.aws.toolkit.jetbrains.core.services.cwc.commands.OptimizeCodeAction.description=Optimizes the selected code
action.aws.toolkit.jetbrains.core.services.cwc.commands.OptimizeCodeAction.text=Optimize Code
action.aws.toolkit.jetbrains.core.services.cwc.commands.RefactorCodeAction.description=Refactors the selected code
action.aws.toolkit.jetbrains.core.services.cwc.commands.RefactorCodeAction.text=Refactor Code
action.aws.toolkit.jetbrains.core.services.cwc.commands.SendToPromptAction.description=Sends selected code to chat
action.aws.toolkit.jetbrains.core.services.cwc.commands.SendToPromptAction.text=Send to Prompt
action.aws.toolkit.jetbrains.core.services.cwc.inline.openChat.text=Inline Chat
action.aws.toolkit.open.arn.browser.text=Open ARN in AWS Console
action.aws.toolkit.open.telemetry.viewer.text=View AWS Telemetry
action.aws.toolkit.s3.open.bucket.viewer.prefixed.text=View Bucket with Prefix...
action.aws.toolkit.s3.open.bucket.viewer.text=View Bucket
action.aws.toolkit.toolwindow.explorer.newConnection.text=Setup authentication to begin
action.aws.toolkit.toolwindow.newConnection.text=Add Another Connection...
action.dynamic.open.text=Open Resource...
action.q.openchat.text=Open Chat Panel
amazonqChat.project_context.index_in_progress=By the way, I'm still indexing this project for full context from your workspace. I may have a better response in a few minutes when it's complete if you'd like to try again then.
amazonqChat.stopChatResponse=You stopped your current work, please provide additional examples or ask another question.
amazonqDoc.answer.codeResult=You can accept the changes to your files, or describe any additional changes you'd like me to make.
amazonqDoc.answer.readmeCreated=I've created a README for your code.
amazonqDoc.answer.readmeUpdated=I've updated your README.
amazonqDoc.edit.message=Okay, let's work on your README. Describe the changes you would like to make. For example, you can ask me to:\n- Correct something\n- Expand on something\n- Add a section\n- Remove a section
amazonqDoc.edit.placeholder=Describe documentation changes
amazonqDoc.error.generating=Unable to generate changes.
amazonqDoc.error_text=I'm sorry, I ran into an issue while trying to generate your documentation. Please try again.
amazonqDoc.exception.content_length_error=The folder you selected is in a project or workspace that is too large for me to use as context. To create or update a README for your code, choose a folder in a smaller project or workspace. For information on quotas, see the <a href="https://docs.aws.amazon.com/amazonq/latest/qdeveloper-ug/doc-generation.html#quotas" target="_blank">Amazon Q Developer documentation.</a>
amazonqDoc.exception.no_change_required=I couldn't find any code changes to update in the README. Try another documentation task.
amazonqDoc.exception.prompt_too_vague=I need more information to make changes to your README. Try providing some of the following details:\n- Which sections you want to modify\n- The content you want to add or remove\n- Specific issues that need correcting\n\nFor more information on prompt best practices, see the <a href="https://docs.aws.amazon.com/amazonq/latest/qdeveloper-ug/doc-generation.html" target="_blank">Amazon Q Developer documentation.</a>
amazonqDoc.exception.prompt_unrelated=These changes don't seem related to documentation. Try describing your changes again, using the following best practices:\n- Changes should relate to how project functionality is reflected in the README\n- Content you refer to should be available in your codebase\n\nFor more information on prompt best practices, see the <a href="https://docs.aws.amazon.com/amazonq/latest/qdeveloper-ug/doc-generation.html" target="_blank">Amazon Q Developer documentation.</a>
amazonqDoc.exception.readme_too_large=The README in your folder is too large for me to review. Try reducing the size of your README, or choose a folder with a smaller README. For more information on quotas, see the <a href="https://docs.aws.amazon.com/amazonq/latest/qdeveloper-ug/doc-generation.html#quotas" target="_blank">Amazon Q Developer documentation.</a>
amazonqDoc.exception.readme_update_too_large=The updated README is too large. Try reducing the size of your README, or asking for a smaller update. For more information on quotas, see the <a href="https://docs.aws.amazon.com/amazonq/latest/qdeveloper-ug/doc-generation.html#quotas" target="_blank">Amazon Q Developer documentation.</a>
amazonqDoc.exception.workspace_empty=The folder you chose did not contain any source files in a supported language. Choose another folder and try again. For more information on supported languages, see the <a href="https://docs.aws.amazon.com/amazonq/latest/qdeveloper-ug/doc-generation.html" target="_blank">Amazon Q Developer documentation.</a>
amazonqDoc.inprogress_message.generating=Generating documentation...
amazonqDoc.progress_message.baseline=This might take a few minutes.
amazonqDoc.progress_message.creating=Okay, I'm creating a README for your project.
amazonqDoc.progress_message.generating=Generating documentation
amazonqDoc.progress_message.scanning=Scanning source files
amazonqDoc.progress_message.summarizing=Summarizing source files
amazonqDoc.progress_message.updating=Okay, I'm updating the README.
amazonqDoc.prompt.canceled_source_folder_selection=It looks like you didn't choose a folder. Choose a folder to continue.
amazonqDoc.prompt.choose_folder_to_continue=Choose a folder to continue
amazonqDoc.prompt.create=Create a README
amazonqDoc.prompt.create.confirmation=Create a README for this project?
amazonqDoc.prompt.folder.change=Change folder
amazonqDoc.prompt.folder.proceed=Yes
amazonqDoc.prompt.placeholder=Choose an option to continue
amazonqDoc.prompt.reject.close_session=End session
amazonqDoc.prompt.reject.message=Your changes have been discarded.
amazonqDoc.prompt.reject.new_task=Start a new documentation task
amazonqDoc.prompt.review.accept=Accept
amazonqDoc.prompt.review.changes=Make changes
amazonqDoc.prompt.review.message=Please review and accept the changes.
amazonqDoc.prompt.update=Update an existing README
amazonqDoc.prompt.update.follow_up.edit=Make a specific change
amazonqDoc.prompt.update.follow_up.sync=Update README to reflect code
amazonqDoc.session.create=Create documentation for a specific folder
amazonqDoc.session.sync=Sync documentation
amazonqFeatureDev.chat_message.ask_for_new_task=What new task would you like to work on?
amazonqFeatureDev.chat_message.closed_session=Okay, I've ended this chat session. You can open a new tab to chat or start another workflow.
amazonqFeatureDev.chat_message.devFileInRepository=I noticed that your repository has a `devfile.yaml`. Would you like me to use the devfile to build and test your project as I generate code?\n\nFor more information on using devfiles to improve code generation, see the [Amazon Q Developer documentation](https://docs.aws.amazon.com/amazonq/latest/qdeveloper-ug/software-dev.html).
amazonqFeatureDev.chat_message.generate_dev_file=For future tasks in this project, I can create a devfile to build and test code as I generate it. This can improve the quality of generated code. To allow me to create a devfile, choose **Generate devfile to build code**.
amazonqFeatureDev.chat_message.requesting_changes=Requesting changes ...
amazonqFeatureDev.chat_message.setting_updated=I've updated your settings so I can run code and test commands based on your devfile for this project. You can update this setting under **Amazon Q: Allow Q /dev to run code and test commands**.
amazonqFeatureDev.chat_message.start_code_generation=Okay, I'll generate code for that. This might take a few minutes.\n\nYou can navigate away from this chat, but please keep this tab open. I'll notify you when I'm done.
amazonqFeatureDev.chat_message.start_code_generation_retry=Okay, I'll generate new code. This might take a few minutes.\n\nYou can navigate away from this chat, but please keep this tab open. I'll notify you when I'm done.
amazonqFeatureDev.chat_message.uploading_code=Uploading code...
amazonqFeatureDev.code_generation.error_message=I'm sorry, I ran into an issue while trying to generate your code. Please try again.
amazonqFeatureDev.code_generation.failed_generation=Code generation failed
amazonqFeatureDev.code_generation.generating_code=Generating code ...
amazonqFeatureDev.code_generation.iteration_counts=Would you like me to add this code to your project, or provide feedback for new code? You have {0} out of {1} code generations left.
amazonqFeatureDev.code_generation.iteration_counts_ask_to_add_code=Would you like me to add this code to your project?
amazonqFeatureDev.code_generation.iteration_counts_ask_to_add_code_or_feedback=Would you like me to add this code to your project, or provide feedback for new code?
amazonqFeatureDev.code_generation.iteration_limit.error_text=Sorry, you've reached the quota for number of iterations on code generation. You can insert this code in your files or discuss a new plan. For more information on quotas, see the [Amazon Q Developer documentation](https://docs.aws.amazon.com/amazonq/latest/qdeveloper-ug/software-dev.html#quotas).
amazonqFeatureDev.code_generation.no_file_changes=Unable to generate any file changes
amazonqFeatureDev.code_generation.no_retries.error_message=I'm sorry, I'm having trouble generating your code and can't continue at the moment. Please try again later, and share feedback to help me improve.
amazonqFeatureDev.code_generation.notification_message=Your code suggestions from Amazon Q are ready to review
amazonqFeatureDev.code_generation.notification_open_link=Open chat
amazonqFeatureDev.code_generation.notification_title=Amazon Q Developer Agent for software development
amazonqFeatureDev.code_generation.provide_code_feedback=How can I improve the code for your use case?
amazonqFeatureDev.code_generation.stopped_code_generation=I stopped generating your code. If you want to continue working on this task, provide another description. You have {0} out of {1} code generations left.
amazonqFeatureDev.code_generation.stopped_code_generation_no_iteration_count_display=I stopped generating your code. If you want to continue working on this task, provide another description.
amazonqFeatureDev.code_generation.stopped_code_generation_no_iterations=I stopped generating your code. You don't have more iterations left, however, you can start a new session.
amazonqFeatureDev.code_generation.stopping_code_generation=Stopping code generation...
amazonqFeatureDev.code_generation.updated_code=Okay, I updated your code files. Would you like to work on another task?
amazonqFeatureDev.content_length.error_text=The folder you selected is too large for me to use as context. Please choose a smaller folder to work on. For more information on quotas, see the [Amazon Q Developer documentation](https://docs.aws.amazon.com/amazonq/latest/qdeveloper-ug/software-dev.html#quotas). 
amazonqFeatureDev.error_text=Sorry, we encountered a problem when processing your request.
amazonqFeatureDev.example_text=You can use /dev to:\n- Add a new feature or logic\n- Write tests\n- Fix a bug in your project\n- Generate a README for a file, folder, or project\n\nTo learn more, visit the [Amazon Q Developer User Guide](https://docs.aws.amazon.com/amazonq/latest/qdeveloper-ug/software-dev.html)
amazonqFeatureDev.exception.conversation_not_found=I'm sorry, I'm having technical difficulties at the moment. Please try again.
amazonqFeatureDev.exception.export_parsing_error=I'm sorry, I'm having trouble producing your code. Please try again.
amazonqFeatureDev.exception.guardrails=I'm sorry, I'm having trouble generating your code. Please try again.
amazonqFeatureDev.exception.insert_code_failed=Failed to insert code changes
amazonqFeatureDev.exception.monthly_limit_error=You've reached the monthly quota for the Amazon Q agent for software development. You can try again next month. For more information on usage limits, see the [Amazon Q Developer pricing page](https://aws.amazon.com/q/developer/pricing/).
amazonqFeatureDev.exception.no_change_required_exception=I'm sorry, I ran into an issue while trying to generate your code.\n\n- `/dev` can generate code to make a change in your project. Provide a detailed description of the new feature or code changes you want to make, including the specifics of what the code should achieve.\n\n- To ask me to explain, debug, or optimize your code, you can close this chat tab to start a new conversation.
amazonqFeatureDev.exception.open_diff_failed=Failed to open diff
amazonqFeatureDev.exception.prompt_refusal=I'm sorry, I can't generate code for your request. Please make sure your message and code files comply with the [AWS Responsible AI Policy](https://aws.amazon.com/machine-learning/responsible-ai/policy/).
amazonqFeatureDev.exception.request_failed=Request failed
amazonqFeatureDev.exception.retry_request_failed=Retry request failed
amazonqFeatureDev.exception.throttling=I'm sorry, I'm experiencing high demand at the moment and can't generate your code. This attempt won't count toward usage limits. Please try again.
amazonqFeatureDev.exception.upload_code=I'm sorry, I couldn't upload your workspace artifacts to Amazon S3 to help you with this task. You might need to allow access to the S3 bucket. For more information, see the [Amazon Q documentation](https://docs.aws.amazon.com/amazonq/latest/qdeveloper-ug/security_iam_manage-access-with-policies.html#data-perimeters) or contact your network or organization administrator.
amazonqFeatureDev.exception.upload_url_expiry=I'm sorry, I wasn't able to generate code. A connection timed out or became unavailable. Please try again or check the following:\n\n- Exclude non-essential files in your workspace's `.gitignore`.\n\n- Check that your network connection is stable.
amazonqFeatureDev.follow_instructions_for_authentication=Follow instructions to re-authenticate ...
amazonqFeatureDev.follow_up.accept_for_project=Yes, use my devfile for this project
amazonqFeatureDev.follow_up.close_session=No, thanks
amazonqFeatureDev.follow_up.continue=Continue
amazonqFeatureDev.follow_up.decline_for_project=No, thanks
amazonqFeatureDev.follow_up.generate_dev_file=Generate devfile to build code
amazonqFeatureDev.follow_up.incorrect_source_folder=The folder you chose isn't in your open workspace folder. You can add this folder to your workspace, or choose a folder in your open workspace.
amazonqFeatureDev.follow_up.insert_all_code=Accept all changes
amazonqFeatureDev.follow_up.insert_remaining_code=Accept remaining changes
amazonqFeatureDev.follow_up.modified_source_folder=Changed source root to: {0}
amazonqFeatureDev.follow_up.modify_source_folder=Select files for context
amazonqFeatureDev.follow_up.new_task=Yes, I have another task
amazonqFeatureDev.follow_up.provide_feedback_and_regenerate=Provide feedback & regenerate
amazonqFeatureDev.follow_up.retry=Retry
amazonqFeatureDev.follow_up.send_feedback=Send feedback
amazonqFeatureDev.no_retries.error_text=I'm sorry, I'm having technical difficulties and can't continue at the moment. Please try again later, and share feedback to help me improve.
amazonqFeatureDev.placeholder.additional_improvements=Describe your task or issue in detail
amazonqFeatureDev.placeholder.after_code_generation=Choose an option to proceed
amazonqFeatureDev.placeholder.after_monthly_limit=Chat input is disabled
amazonqFeatureDev.placeholder.closed_session=Open a new chat tab to continue
amazonqFeatureDev.placeholder.context_gathering_complete=Gathering context...
amazonqFeatureDev.placeholder.downloading_and_extracting_lsp_artifacts=Downloading and Extracting LSP Artifacts...
amazonqFeatureDev.placeholder.generating_code=Generating code...
amazonqFeatureDev.placeholder.lsp=LSP
amazonqFeatureDev.placeholder.new_plan=Describe your task or issue in as much detail as possible
amazonqFeatureDev.placeholder.node_runtime_message=Please provide the absolute path of your node js v18+ runtime executable in Settings. Re-open IDE to apply this change.
amazonqFeatureDev.placeholder.node_runtime_path=Node Runtime Path
amazonqFeatureDev.placeholder.provide_code_feedback=Provide feedback or comments
amazonqFeatureDev.placeholder.select_lsp_artifact=Select LSP Artifact
amazonqFeatureDev.placeholder.write_new_prompt=Write a new prompt
apprunner.action.configure=Configure Service
apprunner.action.create.service=Create Service...
apprunner.action.delete.service=Delete Service...
apprunner.action.deploy=Deploy
apprunner.action.deploy.failed=Deployment Failed
apprunner.action.deploy.starting=Starting deployment
apprunner.action.deploy.unableToFindLogStream=Unable to find the log stream for deployment {0}
apprunner.action.pause=Pause...
apprunner.action.pause.confirm=Pause
apprunner.action.resume=Resume...
apprunner.action.resume.confirm=Resume
apprunner.creation.failed=Failed to create App Runner service
apprunner.creation.panel.cpu=CPU:
apprunner.creation.panel.cpu.missing=Select a CPU configuration
apprunner.creation.panel.deployment=Deployment type:
apprunner.creation.panel.deployment.automatic=Automatic
apprunner.creation.panel.deployment.automatic.tooltip=App Runner monitors your registry and deploys a new version of your service for each new revision
apprunner.creation.panel.deployment.manual=Manual
apprunner.creation.panel.deployment.manual.tooltip=Start each deployment yourself using the AWS CLI, Console, or Toolkit
apprunner.creation.panel.environment=Environment Variables:
apprunner.creation.panel.image.access_role=ECR access role:
apprunner.creation.panel.image.access_role.missing=Missing ECR access role for App Runner
apprunner.creation.panel.image.access_role.tooltip=This role gives App Runner permission to access ECR
apprunner.creation.panel.image.uri=Container image URI:
apprunner.creation.panel.image.uri.missing=Enter container image URI
apprunner.creation.panel.memory=Memory:
apprunner.creation.panel.memory.missing=Select memory amount
apprunner.creation.panel.name=Service name:
apprunner.creation.panel.name.missing=Enter a service name
apprunner.creation.panel.port=Port:
apprunner.creation.panel.repository.api=Configure all settings here
apprunner.creation.panel.repository.api.tooltip=Specify all settings for your service on creation
apprunner.creation.panel.repository.branch=Branch:
apprunner.creation.panel.repository.build_command=Build Command:
apprunner.creation.panel.repository.build_command.missing=Enter a build command
apprunner.creation.panel.repository.build_command.tooltip=This command runs in the root directory of your repository when a new code version is deployed. Use it to install dependencies or compile your code.
apprunner.creation.panel.repository.configuration=Configuration:
apprunner.creation.panel.repository.connection=Connection:
apprunner.creation.panel.repository.connection.help=App Runner deploys your source code by installing an app called "AWS Connector for GitHub" in your GitHub account. This connection must be set up through the console.
apprunner.creation.panel.repository.connection.missing=Select a connection
apprunner.creation.panel.repository.file=Provide a configuration file
apprunner.creation.panel.repository.file.tooltip=App Runner will read your configuration from the apprunner.yaml file in the root of your repository
apprunner.creation.panel.repository.runtime=Runtime:
apprunner.creation.panel.repository.runtime.missing=Select a runtime
apprunner.creation.panel.repository.runtime.tooltip=Runtime determines what tools are available in the build and runtime environments
apprunner.creation.panel.repository.url=Repository URL:
apprunner.creation.panel.repository.url.tooltip=The URL of the Git repository (e.g. "https://github.com/aws/aws-toolkit-jetbrains")
apprunner.creation.panel.source=Source:
apprunner.creation.panel.source.ecr=ECR
apprunner.creation.panel.source.ecr_public=ECR public
apprunner.creation.panel.source.repository=Source code repository
apprunner.creation.panel.start_command=Start Command:
apprunner.creation.panel.start_command.image.tooltip=The applicationâs container runs this command on launch. Leave blank to use the entry point command defined in the container image.
apprunner.creation.panel.start_command.missing=Enter a start command
apprunner.creation.panel.start_command.repo.tooltip=This command runs in the root directory of your service to start the service processes. Use it to start a webserver for your service.
apprunner.creation.started=Started creating App Runner service
apprunner.creation.started.title=App Runner
apprunner.creation.title=Create App Runner Service
apprunner.pause.failed=Failed to pause service {0}!\n{1}
apprunner.pause.succeeded=Pausing service {0}
apprunner.pause.warning=<html><b>Your service will be unavailable while paused.</b><br/><br/>You can resume the service once the pause operation is complete.<br/><br/>Click pause to proceed pausing {0}</html>
apprunner.resume.failed=Failed to resume {0}!\n{1}
apprunner.resume.succeeded=Resuming {0}
apprunner.resume.warning=Resume {0}?
apprunner.service.configure.title=Configure Service {0}
apprunner.service.resource_type=App Runner Service
apprunner.view_application_log_streams=View Application Log Streams
apprunner.view_service_log_streams=View Service Log Streams
apprunner.view_service_log_streams.error=Unable to open log group {0}
apprunner.view_service_log_streams.error_not_created=App Runner log group has not been created
aws.codewhispererq.tab.title=Amazon Q
aws.description=Amazon Web Services (AWS) is a secure cloud services platform, offering compute power, database storage, content delivery and other functionality to help businesses scale and grow.
aws.developer.tools.tab.title=CodeCatalyst
aws.getstarted.auth.panel.notSupport_text=Not supported for this feature
aws.getstarted.auth.panel_bullet_iam=IAM Credentials
aws.getstarted.auth.panel_bullets=IAM Identity Center\nAWS Builder ID
aws.getstarted.resource.panel_description=Work with S3, CloudWatch and more.
aws.getstarted.resource.panel_question_text=Don't have an AWS account?
aws.getstarted.resource.panel_title=AWS Explorer
aws.notification.auto_update.feature_intro.ok=OK
aws.notification.auto_update.feature_intro.title=AWS plugins will now auto update
aws.notification.auto_update.settings.title=Manage auto-updates
aws.notification.auto_update.title={0} plugin updated
aws.notification.credentials_missing=Error: AWS credentials not configured
aws.notification.do_not_show_again=Don't show again
aws.notification.sam_cli_not_valid=Error: {0}. Click <a href="">here</a> to reconfigure AWS SAM.
aws.notification.title=AWS Toolkit
aws.notification.title.amazonq=Amazon Q
aws.notification.title.amazonq.feature_dev=Amazon Q FeatureDev
aws.notification.title.amazonq.test_generation=Amazon Q Test Generation
aws.notification.title.codewhisperer=Amazon Q
aws.onboarding.getstarted.panel.bottom_text=<a href="https://docs.aws.amazon.com/toolkit-for-jetbrains/latest/userguide/auth-access.html">Learn more about authenticating with the Toolkit</a>
aws.onboarding.getstarted.panel.bottom_text_question=Why do these have different authentication requirements?
aws.onboarding.getstarted.panel.builderid_row_comment_text=Personal profile for builders
aws.onboarding.getstarted.panel.button_iam_login=Authenticate with IAM
aws.onboarding.getstarted.panel.comment_link_doc=Documentation
aws.onboarding.getstarted.panel.comment_link_github=Join us on GitHub
aws.onboarding.getstarted.panel.group_title=Select a feature to setup authentication
aws.onboarding.getstarted.panel.iam_row_comment_text=Long-term programmatic access
aws.onboarding.getstarted.panel.idc_row_comment_text=Successor to AWS Single Sign-on
aws.onboarding.getstarted.panel.login_with_iam=<a>Use Professional License</a>
aws.onboarding.getstarted.panel.share_feedback=<a>Share Feedback</a>
aws.onboarding.getstarted.panel.signup_iam_text=Sign up for free
aws.onboarding.getstarted.panel.title=Authenticate with AWS Toolkit
aws.q.lsp.client.diff_message=(Generated by Amazon Q)
aws.q.migration.action.install.text=Install
aws.q.migration.action.manage_plugins.text=Manage plugins
aws.q.migration.action.read_more.text=Read more
aws.q.migration.action.restart.text=Restart
aws.q.migration.existing_users.notify.message=We've auto-installed it for you with all the same features and settings from CodeWhisperer and Amazon Q chat
aws.q.migration.existing_users.notify.title=Amazon Q is now its own plugin
aws.q.migration.failed_to_install.message=Amazon Q failed to install, click below to go to the plugin marketplace and install the plugin manually.
aws.q.migration.new_users.notify.message=Install it to use Amazon Q, a generative AI assistant, with chat and code suggestions.
aws.q.migration.new_users.notify.title=Amazon Q has moved to its own plugin
aws.settings.auto_detect=Auto-detected: {0}
aws.settings.auto_update.notification.message=Restart your IDE to apply the update.
aws.settings.auto_update.notification.no=Not now
aws.settings.auto_update.notification.yes=Restart
aws.settings.auto_update.notification_enable.text=Show notification on update success
aws.settings.auto_update.notification_enable.tooltip=If unchecked, updates will still get applied upon the next IDE restart.
aws.settings.auto_update.progress.message=Updating AWS plugins
aws.settings.auto_update.text=Automatically install plugin updates when available
aws.settings.aws_cli_settings=AWS CLI Settings
aws.settings.codewhisperer.automatic_import_adder=Imports recommendation
aws.settings.codewhisperer.automatic_import_adder.tooltip=Amazon Q will add import statements with code suggestions when necessary
aws.settings.codewhisperer.code_review=Code Review
aws.settings.codewhisperer.code_review.description=Specifies a list of code issue identifiers(separated by ";") that Amazon Q should ignore when reviewing your workspace. Each item in the array should be a unique string identifier for a specific code issue. This allows you to suppress notifications for known issues that you've assessed and determined to be false positives or not applicable to your project. Use this setting with caution, as it may cause you to miss important security alerts.
aws.settings.codewhisperer.code_review.title=Ignored Security Issues
aws.settings.codewhisperer.configurable.controlled_by_admin=\      Controlled by your admin
aws.settings.codewhisperer.configurable.opt_out.title=Share Amazon Q content with AWS
aws.settings.codewhisperer.configurable.opt_out.tooltip=When checked, your content processed by Amazon Q may be used for service improvement (except for content processed by the Amazon Q Developer Pro tier). Unchecking this box will cause AWS to delete any of your content used for that purpose. The information used to provide the Amazon Q service to you will not be affected. See the <a href="https://aws.amazon.com/service-terms/">Service Terms</a> for more detail.
aws.settings.codewhisperer.configurable.title=Amazon Q
aws.settings.codewhisperer.feature_development=Feature Development
aws.settings.codewhisperer.feature_development.allow_running_code_and_test_commands=Allow /dev to run code and test commands
aws.settings.codewhisperer.group.data_sharing=Data Sharing
aws.settings.codewhisperer.group.general=General
aws.settings.codewhisperer.group.inline_suggestions=Inline Suggestions
aws.settings.codewhisperer.group.plugin_settings=Plugin Settings
aws.settings.codewhisperer.group.q_chat=Chat
aws.settings.codewhisperer.include_code_with_reference=Include suggestions with code references
aws.settings.codewhisperer.include_code_with_reference.tooltip=When checked, Amazon Q will include suggestions with code references. If you authenticate through IAM Identity Center, this setting is controlled by your Amazon Q Developer Pro administrator. <a href="https://docs.aws.amazon.com/amazonq/latest/qdeveloper-ug/code-reference.html">Learn more<a/>
aws.settings.codewhisperer.project_context=Workspace index
aws.settings.codewhisperer.project_context.tooltip=When you add @workspace to your questions in Amazon Q chat, Amazon Q will index your workspace files locally to use as context for its response. Extra CPU usage is expected while indexing a workspace. This will not impact Amazon Q features or your IDE, but you may manage CPU usage by setting the number of local threads below.
aws.settings.codewhisperer.project_context_gpu=Workspace index uses GPU
aws.settings.codewhisperer.project_context_gpu.tooltip=Enable GPU to help index your local workspace files. This setting only applies to Linux and Windows.
aws.settings.codewhisperer.project_context_index_max_size=Workspace index max size
aws.settings.codewhisperer.project_context_index_max_size.tooltip=The maximum size of local workspace files to be indexed in MB.
aws.settings.codewhisperer.project_context_index_thread=Workspace index worker threads
aws.settings.codewhisperer.project_context_index_thread.tooltip=Number of worker threads of Amazon Q local index process. Set to 0 to use system default worker threads for balanced performance. Please restart or reload IntelliJ after changing worker threads.
aws.settings.codewhisperer.warning=To use Amazon Q, login with AWS Builder ID or AWS IAM Identity Center.
aws.settings.codewhisperer.workspace_context=Workspace context
aws.settings.codewhisperer.workspace_context.tooltip=When checked, Amazon Q will enable server side project context.
aws.settings.dynamic_resources_configurable.clear_all=Clear All
aws.settings.dynamic_resources_configurable.select_all=Select All
aws.settings.dynamic_resources_configurable.suggest_types.dialog.message=Please suggest additional AWS resource types (e.g. AWS::S3::Bucket)\nyou would like to see supported in future releases.\n\n(max length: 2000 chars)
aws.settings.dynamic_resources_configurable.suggest_types.dialog.title=Additional Resource Type Suggestions
aws.settings.dynamic_resources_configurable.suggest_types.prompt=Don't see a resource type you expected?
aws.settings.dynamic_resources_configurable.title=Resources
aws.settings.executables.auto_resolved=(Auto-resolved: {0})
aws.settings.executables.cannot_determine_version=Cannot determine version of {0}: {1}
aws.settings.executables.executable_invalid=Invalid {0} executable: {1}
aws.settings.executables.find.description=Select path to {0} executable
aws.settings.executables.resolution_exception=Exception occurred attempting to resolve {0} executable: {1}
aws.settings.find.description=Select path to {0} CLI executable
aws.settings.find.title={0} CLI Configuration
aws.settings.global_label=Global Settings
aws.settings.lambda.configurable.title=Lambda
aws.settings.learn_more=Learn more
aws.settings.sam.location=SAM CLI executable:
aws.settings.sam.show_all_gutter_icons=Show gutter icons for all potential AWS Lambda handlers
aws.settings.sam.show_all_gutter_icons_tooltip=When enabled, show gutter icons in source files for all potential Lambda handlers, even if they are not defined in a template or remotely.
aws.settings.serverless_label=Serverless Settings
aws.settings.show.label=Show AWS Settings
aws.settings.telemetry.description=Help us improve our product! Allow us to collect basic usage data.
aws.settings.telemetry.option=Send usage metrics to AWS
aws.settings.telemetry.prompt.message=Usage metrics are collected by default. Click <a href="">here</a> to adjust this behavior.
aws.settings.telemetry.prompt.title=AWS IDE plugins telemetry
aws.settings.title=AWS
aws.settings.toolkit.configurable.title=AWS Toolkit
aws.sso.signing.device.code=Proceed To Browser
aws.sso.signing.device.code.copy=Copy Code
aws.sso.signing.device.code.copy.dialog.text=To proceed, open the login page and confirm that the code matches:
aws.sso.signing.device.waiting=Waiting for browser authorization for code: {0}
aws.terminal.action=Open AWS local terminal
aws.terminal.action.tooltip=Start a local terminal with the current AWS connection settings injected as environment variables
aws.terminal.exception.failed_to_resolve_credentials=Unable to open AWS-aware Terminal, unable to resolve credentials: {0}
aws.terminal.exception.invalid_credentials=Unable to open AWS-aware Terminal, credentials aren't valid: {0}
aws.toolkit.experimental.description=Warning: These features are experimental, may contain bugs or usability problems, and may be<br/>removed from the AWS Toolkit at any time.
aws.toolkit.experimental.enable=Enable
aws.toolkit.experimental.suggestion.description=The experimental feature "{0}" adds the following capability:<br/>{1}.<br/>Do you want to enable it?
aws.toolkit.experimental.suggestion.title=AWS Toolkit Experiment Suggestion
aws.toolkit.experimental.title=Experimental Features
aws.toolkit_deprecation.message=Support for {0} {1} is being deprecated - an upcoming release will require a version based on {2} or newer.
aws.toolkit_deprecation.message.gateway=Upgrade JetBrains Gateway to version {2} or newer to use the next release of the toolkit. Support for {0} {1} is being deprecated.
aws.toolkit_deprecation.title=AWS Toolkit deprecation notice
aws_builder_id.service_name=AWS Builder ID
aws_builder_id.sign_out=Sign out of AWS Builder ID
aws_connection.credentials.label=Credentials:
aws_connection.region.label=Region:
aws_connection.tab.label=AWS Connection
caws.add_repository=Add a repository to this project
caws.add_workspace=Create Dev Environment
caws.alias.instruction.text=Aliases can contain any combination of letters, numbers, dashes and underlines.
caws.backend.error.expired=The IDE backend is expired. Please create a new Dev Environment or update the Dev Environment settings.
caws.backend.error.unknown=Starting the IDE backend failed with exit code {0}. Any available backend logs are available below.
caws.branch_title=Branch: {0}
caws.check_connection=Check Connection and Continue
caws.clone.invalid_pat=Invalid PAT
caws.clone.invalid_pat.help=<html>Cloning may have failed due to an invalid <a href='https://docs.aws.amazon.com/codecatalyst/latest/userguide/concepts.html#personal-access-token-concept'>access token</a>. Would you like the toolkit to recreate your access token?</html>
caws.clone_dialog_description=Clone repository from Amazon CodeCatalyst
caws.clone_dialog_directory=Directory:
caws.clone_dialog_repository_loading_error=Failed to load repositories
caws.clone_dialog_title=Clone Repository
caws.compute.size.in.free.tier.comment=Some compute and storage options are not currently available for your space. These require upgrading the billing tier.
caws.configure_workspace=Configure
caws.configure_workspace_failed=Reconfigure Dev Environment Failed
caws.configure_workspace_not_running=Reconfigure Dev Environment is only available for running environments
caws.configure_workspace_tab_save_button=Save and restart
caws.configure_workspace_tab_title=Configure Dev Environment
caws.configure_workspace_title=Configure Dev Environment: {0} in {1}
caws.connected.builder_id=\   AWS Builder ID Connected
caws.connected.identity_center=\   IAM Identity Center Connected {0}
caws.connecting.checking=Checking state of Dev Environment
caws.connecting.in_progress=Connecting to Dev Environment
caws.connecting.waiting_for_environment=Launching Dev Environment (This may take a few minutes)
caws.connection_progress_panel_title=Amazon CodeCatalyst Dev Environment: {0}
caws.copy.url.select_repository=Select a repository to clone
caws.create_workspace=Create Dev Environment
caws.create_workspace_description=Create Dev Environments for your Amazon CodeCatalyst projects
caws.creating_branch=Creating source repository branch
caws.creating_project=Creating Project
caws.creating_workspace=Creating Dev Environment
caws.credential.setup.invalid_credentials_error=Credentials provided are not valid
caws.credential.setup.invalid_credentials_error_title=Invalid Credentials
caws.credential.setup.profile=AWS Profile
caws.credential.setup.requirements_message="You need <a href=\\"https://docs.aws.amazon.com/cli/latest/userguide/cli-configure-files.html\\">AWS credentials</a> to use this feature"
caws.credential.setup_access_key=AWS Access Key ID:
caws.credential.setup_access_key_validation_text=Access Key ID must not be empty
caws.credential.setup_default_region=Default Region:
caws.credential.setup_profile_name=Name:
caws.credential.setup_profile_name_validation_text=Profile name must not be empty
caws.credential.setup_secret_access_key=AWS Secret Access Key:
caws.credential.setup_secret_access_key_validation_text=Secret Access Key must not be empty
caws.delete_failed=Delete Dev Environment Failed
caws.delete_workspace=Delete
caws.delete_workspace_warning=Are you sure you wish to delete the Dev Environment? All data will be deleted
caws.delete_workspace_warning_title=Confirm Deletion
caws.devenv.continue.working.after.timeout=Your dev environment has had no activity in the past {0} minutes and will be terminated within 5 minutes. Press OK to continue working
caws.devenv.continue.working.after.timeout.title=Do you want to continue working?
caws.devfile.schema=Devfile Schema
caws.devtoolPanel.fetch.git.url=Fetching Git clone URL for {0}
caws.devtoolPanel.git_url_copied=Clone URL copied to clipboard
caws.devtoolPanel.title=CodeCatalyst
caws.download.thin_client=Downloading IDE thin client
caws.environment.status=Dev Environment is currently: {0}
caws.environment.view_pricing=Learn more about Dev Environment pricing
caws.expired.connection=\   Expired Connection
caws.free.tier.subscription.storage=Other storage options are not currently available for your space. These require upgrading the billing tier.
caws.getstarted.panel.description=Spend more time coding and less <br>time managing development <br>environments.
caws.getstarted.panel.link_text=Learn more about CodeCatalyst Spaces
caws.getstarted.panel.login=Connect to CodeCatalyst
caws.getstarted.panel.question.text=Don't have a CodeCatalyst Space?
caws.ide_version_validation_text=IDE version must be selected
caws.information.panel=A thin client on your machine makes development feel local, even when the heavy lifting is done on Amazon CodeCatalyst.
caws.information_panel=Create an on-demand Amazon CodeCatalyst Dev Environment to work on your code in the cloud.
caws.list_workspaces_failed=Failed to list Dev Environments
caws.loading.panel.credential_validation_text=Please select a credential profile from the drop down
caws.login=Sign in
caws.no_repo=No repository
caws.no_spaces=No spaces available
caws.one.branch.per.dev.env.comment=You cannot create more than one Dev Environment for a branch. If the chosen branch already has a Dev Environment, resume the existing Dev Environment.
caws.open.devfile=Open Devfile
caws.open.devfile.failed=Unable to open Devfile
caws.pause_action=Pause
caws.pause_warning=Are you sure you wish to pause the Dev Environment?
caws.pause_warning_title=Confirm Pause
caws.pause_workspace_failed=Pause Dev Environment Failed
caws.project=Project:
caws.project.autocreate.description=Created by AWS Toolkit for JetBrains
caws.rebuild.devfile.failed=Dev Environment rebuild with the provided Devfile failed: {0}
caws.rebuild.devfile.failed_server=Dev Environment returned the following:<br/>{0}
caws.rebuild.failed.title=Rebuild Dev Environment
caws.rebuild.workspace.notification=Dev Environment requires rebuild
caws.reconnect.wait_for_client=Waiting for client to gracefully close
caws.repository=Source repository:
caws.spaces.error_loading=Error loading spaces
caws.spaces.refresh=Refresh spaces
caws.sso_profile_configuration_instruction_message=To configure SSO profile use the AWS CLI
caws.storage.value={0} GiB
caws.title=Amazon CodeCatalyst
caws.update_dev_environment=Update Dev Environment
caws.update_dev_environment.failed=Failed to update Dev Environment
caws.update_devfile=Update Dev Environment with this Devfile? This action will trigger a Dev Environment restart.
caws.update_devfile.failed=Failed to update Devfile
caws.update_devfile_title=Update Dev Environment
caws.updating_devfile=Updating Dev Environment with Devfile...
caws.view.projects_web=View project summary in Amazon CodeCatalyst...
caws.view.workspaces_web=View all Dev Environments for project in Amazon CodeCatalyst...
caws.workspace.backend.title=Amazon CodeCatalyst Dev Environment
caws.workspace.clone.info=The project source repository will be cloned to the Dev Environment directly.
caws.workspace.clone.info_repo=Choose the branch to clone, or create a branch for your work in this Dev Environment.
caws.workspace.clone.ssh_agent=Your SSH agent will be forwarded to the Dev Environment for authentication to the repository.
caws.workspace.connection.failed=Dev Environment connection failed
caws.workspace.creation.failed=Dev Environment creation failed
caws.workspace.details.additional_settings_header=Additional Settings
caws.workspace.details.alias.label=Dev Environment alias:
caws.workspace.details.backend_toolkit_location=Backend Toolkit Location:
caws.workspace.details.branch_existing=Existing Branch:
caws.workspace.details.branch_new=New Branch:
caws.workspace.details.branch_new_validation=New branch name is required
caws.workspace.details.branch_title=Branch:
caws.workspace.details.branch_validation=Branch selection is required
caws.workspace.details.clone_repo=Clone a repository
caws.workspace.details.create_branch_comment=Choose an existing branch or create a new branch. If you choose a third-party source repository, you must work in an existing branch.
caws.workspace.details.create_empty_dev_env=Create an empty Dev Environment
caws.workspace.details.create_from_existing_branch=Existing Branch
caws.workspace.details.create_from_new_branch=New Branch
caws.workspace.details.developer_tool_settings=Toolkit Developer Settings
caws.workspace.details.inactivity_timeout=Timeout:
caws.workspace.details.inactivity_timeout_comment=Dev Environment will be stopped after inactivity timeout.
caws.workspace.details.introduction_message=Create Amazon CodeCatalyst projects to start using Dev Environments.
caws.workspace.details.last_used=Last Used: {0}
caws.workspace.details.no_timeout=No timeout
caws.workspace.details.persistent_storage_comment=Storage cannot be edited from CodeCatalyst or IDE after creating Dev Environment.
caws.workspace.details.persistent_storage_title=Storage:
caws.workspace.details.project.comment=A Dev Environment must be associated with a project
caws.workspace.details.project.required=Project name must be provided
caws.workspace.details.project.title=Project name:
caws.workspace.details.project_specific_title=New Amazon CodeCatalyst Dev Environment for {0}
caws.workspace.details.project_validation=Project selection is required
caws.workspace.details.repository_validation=Repository selection is required
caws.workspace.details.s3_bucket=S3 Staging Bucket:
caws.workspace.details.select_org=Select a space above to continue
caws.workspace.details.title=New Amazon CodeCatalyst Dev Environment
caws.workspace.details.toolkit_location=Toolkit Location:
caws.workspace.details.unlinked_repo_url=Repo URL:
caws.workspace.details.use_bundled_toolkit=Use Bundled Toolkit
caws.workspace.ide_label=IDE: 
caws.workspace.incompatible=(incompatible)
caws.workspace.instance_size=Compute:
caws.workspace.labels_label=Label: 
caws.workspace.list_panel_search_empty_text=Search Dev Environments
caws.workspace.new=Create Dev Environment
caws.workspace.panel_other_repos=Other repositories
caws.workspace.settings=Dev Environment Configuration
caws.workspace.settings.repository_header=Repository
caws.write_credentials_to_file_progress=Writing credentials to shared credentials file
cloudformation.capabilities=CloudFormation Capabilities:
cloudformation.capabilities.auto_expand=Auto Expand
cloudformation.capabilities.auto_expand.toolTipText=Allows expansion of macros in templates
cloudformation.capabilities.iam=IAM
cloudformation.capabilities.iam.toolTipText=Allows templates that contain IAM resources to be deployed
cloudformation.capabilities.named_iam=Named IAM
cloudformation.capabilities.named_iam.toolTipText=Allows templates that contain IAM resources with custom names to be deployed
cloudformation.capabilities.toolTipText=CloudFormation capabilities represent potentially dangerous actions that must be user acknowledged for CloudFormation to perform.
cloudformation.create_stack.failed=Failed to create stack {0}: {1}
cloudformation.create_stack.failed_validation=Failed to create stack {0} due to validation error
cloudformation.create_stack.timeout=Failed to create stack {0} in {1} seconds. View the latest status of the stack via the AWS Console.
cloudformation.delete_stack.failed=Failed to delete stack {0}: {1}
cloudformation.delete_stack.timeout=Failed to delete stack {0} in {1} seconds. View the latest status of the stack via the AWS Console.
cloudformation.execute_change_set.failed=Failed to execute change set against {0}
cloudformation.execute_change_set.success=Successfully executed change set against {0}
cloudformation.execute_change_set.success.title=Successfully executed change set
cloudformation.invalid_property=Property {0} has invalid value {1}
cloudformation.key_not_found={0} not found on resource {1}
cloudformation.missing_property=Property {0} not found in {1}
cloudformation.service_name=AWS CloudFormation
cloudformation.stack.delete.action=Delete Stack...
cloudformation.stack.filter.show_completed=Show Completed
cloudformation.stack.logical_id=Logical ID
cloudformation.stack.logical_id.copy=Copy Logical ID
cloudformation.stack.outputs.description=Description
cloudformation.stack.outputs.export=Export Name
cloudformation.stack.outputs.export.copy=Copy Export Name
cloudformation.stack.outputs.key=Key
cloudformation.stack.outputs.key.copy=Copy Key
cloudformation.stack.outputs.value=Value
cloudformation.stack.outputs.value.copy=Copy Value
cloudformation.stack.physical_id=Physical ID
cloudformation.stack.physical_id.copy=Copy Physical ID
cloudformation.stack.reason=Status Reason
cloudformation.stack.status=Status
cloudformation.stack.tab_labels.events=Events
cloudformation.stack.tab_labels.outputs=Outputs
cloudformation.stack.tab_labels.resources=Resources
cloudformation.stack.type=Type
cloudformation.stack.view=View Stack Status
cloudformation.template_index.missing_type=Resource type must not be null for indexing
cloudformation.toolwindow.label=CloudFormation
cloudformation.update_stack.failed=Failed to update stack {0}: {1}
cloudformation.update_stack.failed_validation=Failed to update stack {0} due to validation error
cloudformation.update_stack.timeout=Failed to update stack {0} in {1} seconds. View the latest status of the stack via the AWS Console.
cloudformation.yaml.invalid_root_type=Template does not start with a mapping: {0}
cloudformation.yaml.too_many_documents=There should only be 1 YAML document per file: {0}
cloudformation.yaml.too_many_files=Found {0} YAML files but only expected 1
cloudwatch.logs.compare.start.end.date=Start date must be before end date
cloudwatch.logs.delete_log_group=Delete Log Group
cloudwatch.logs.download=Download Log Stream
cloudwatch.logs.download.description=Select a folder to save the Log Stream to
cloudwatch.logs.exception=CloudWatch Logs Exception
cloudwatch.logs.export=Export Log Stream
cloudwatch.logs.failed_to_load_more=Failed to load more
cloudwatch.logs.failed_to_load_stream=Failed to load log stream {0}
cloudwatch.logs.failed_to_load_streams=Failed to load log streams for log group {0}
cloudwatch.logs.failed_to_save_query=Failed to save query
cloudwatch.logs.filter_loggroup=Filter streams by prefix
cloudwatch.logs.filter_logs=Filter events
cloudwatch.logs.filtered_log_stream_title=Filtered Stream: {0} from {1} to {2}
cloudwatch.logs.last_event_time=Last Event Time
cloudwatch.logs.log_group_does_not_exist=Log group {0} does not exist
cloudwatch.logs.log_group_title=Group: {0}
cloudwatch.logs.log_groups=Log Groups
cloudwatch.logs.log_record=Log Record: {0}
cloudwatch.logs.log_record_field=Log Event Field
cloudwatch.logs.log_record_open_log_stream=View Log Stream
cloudwatch.logs.log_record_value=Log Event Value
cloudwatch.logs.log_stream_does_not_exist=Log stream {0} does not exist
cloudwatch.logs.log_stream_title=Stream: {0}
cloudwatch.logs.log_streams=Log Streams
cloudwatch.logs.no_end_date=End Date must be specified
cloudwatch.logs.no_events=No Events in Log Stream
cloudwatch.logs.no_events_query=No Events matching the query found in Log Stream {0}
cloudwatch.logs.no_log_group=At least one log group must be selected
cloudwatch.logs.no_log_streams=No Log Stream found
cloudwatch.logs.no_query_entered=Query must be specified
cloudwatch.logs.no_relative_time_number=Number must be specified
cloudwatch.logs.no_results_found=No results found
cloudwatch.logs.no_start_date=Start Date must be specified
cloudwatch.logs.no_term_entered=Search Term must be specified
cloudwatch.logs.open=View Log Streams
cloudwatch.logs.open_in_editor=Open in Editor
cloudwatch.logs.open_in_editor_failed=Open in editor failed
cloudwatch.logs.open_query_editor=Open Query Editor
cloudwatch.logs.opening_in_editor=Opening Stream {0} in editor
cloudwatch.logs.query=Query
cloudwatch.logs.query.form.ok_Button=Execute
cloudwatch.logs.query_editor_title=Query Log Groups
cloudwatch.logs.query_name=Query Name
cloudwatch.logs.query_name_missing=Query name is missing
cloudwatch.logs.query_result=Query Results
cloudwatch.logs.query_result_completion_status=Result
cloudwatch.logs.query_result_completion_successful=Query Execution Completed
cloudwatch.logs.query_results_table_error=Failed to load Query Results
cloudwatch.logs.query_saved_successfully=Query has been saved to your account!
cloudwatch.logs.query_tab_title=Query: {0}
cloudwatch.logs.save_action=Save to a File
cloudwatch.logs.save_query=Save Query
cloudwatch.logs.save_query_dialog_name=Enter Query Name
cloudwatch.logs.saved_query_status=Saved Query Status
cloudwatch.logs.saving_to_disk=Saving Stream {0} to disk
cloudwatch.logs.saving_to_disk_failed=Saving Stream {0} failed
cloudwatch.logs.saving_to_disk_succeeded=Finished saving Log Stream {0} to {1}
cloudwatch.logs.select_saved_query_dialog_name=Select Saved Query
cloudwatch.logs.show_logs_around=Show Logs Around
cloudwatch.logs.stream_save_to_file=Save {0} to a file
cloudwatch.logs.stream_too_big=Stream is Too Large
cloudwatch.logs.stream_too_big_message=Log stream {0} is too large to open in the editor
cloudwatch.logs.tail=Tail logs
cloudwatch.logs.time_days=Days
cloudwatch.logs.time_hours=Hours
cloudwatch.logs.time_minutes=Minutes
cloudwatch.logs.time_weeks=Weeks
cloudwatch.logs.toolwindow=CloudWatch Logs
cloudwatch.logs.validation.timerange=Time Range not Selected
cloudwatch.logs.view_log_stream=View Log Stream
cloudwatch.logs.view_log_streams=View Log Streams
cloudwatch.logs.wrap=Wrap logs
code.aws=Amazon CodeCatalyst
code.aws.value_prop_text=Amazon CodeCatalyst: Launch developer environments in the cloud within seconds.
code.aws.workspaces=Amazon CodeCatalyst
code.aws.workspaces.short=Dev Environments
codemodernizer.builderrordialog.description.title=Error occurred when building your project
codemodernizer.chat.form.user_selection.item.choose_module=Choose a module to transform
codemodernizer.chat.form.user_selection.item.choose_one_or_multiple_diffs_option=Choose how to receive proposed changes
codemodernizer.chat.form.user_selection.item.choose_skip_tests_option=Choose to skip unit tests
codemodernizer.chat.form.user_selection.item.choose_sql_metadata_file=Okay, I can convert the embedded SQL code for your Oracle to PostgreSQL transformation. To get started, upload the zipped metadata file from your schema conversion in AWS Data Migration Service (DMS). To retrieve the metadata file:\n1. Open your database migration project in the AWS DMS console.\n2. Open the schema conversion and choose **Convert the embedded SQL in your application**.\n3. Once you complete the conversion, close the project and go to the S3 bucket where your project is stored.\n4. Open the folder and find the project folder ("sct-project").\n5. Download the object inside the project folder. This will be a zip file.\n\nFor more info, refer to the [documentation](https://docs.aws.amazon.com/dms/latest/userguide/schema-conversion-embedded-sql.html).
codemodernizer.chat.form.user_selection.item.choose_target_version=Choose the target code version
codemodernizer.chat.form.user_selection.title=Q - Code transformation
codemodernizer.chat.message.absolute_path_detected=I detected {0} potential absolute file path(s) in your {1} file: **{2}**. Absolute file paths might cause issues when I build your code. Any errors will show up in the build log.
codemodernizer.chat.message.auth_prompt=Follow instructions to re-authenticate. If you experience difficulty, sign out of the Amazon Q plugin and sign in again.
codemodernizer.chat.message.button.cancel=Cancel
codemodernizer.chat.message.button.confirm=Confirm
codemodernizer.chat.message.button.hil_cancel=Cancel
codemodernizer.chat.message.button.hil_submit=Submit
codemodernizer.chat.message.button.open_file=Open file
codemodernizer.chat.message.button.open_transform_hub=Open Transformation Hub
codemodernizer.chat.message.button.select_sql_metadata=Select metadata file
codemodernizer.chat.message.button.stop_transform=Stop transformation
codemodernizer.chat.message.button.view_build=View build progress
codemodernizer.chat.message.button.view_diff=View diff
codemodernizer.chat.message.button.view_failure_build_log=View build log
codemodernizer.chat.message.button.view_summary=View summary
codemodernizer.chat.message.changes_applied=I applied the changes to your project.
codemodernizer.chat.message.choose_objective=I can help you with the following tasks:\n- Upgrade your Java 8, Java 11, and Java 17 codebases to Java 17 or Java 21.\n- Upgrade Java 17 or Java 21 code with up-to-date libraries and other dependencies.\n- Convert embedded SQL code for Oracle to PostgreSQL database migrations in AWS DMS.\n\nWhat would you like to do? You can enter "language upgrade" or "sql conversion".
codemodernizer.chat.message.choose_objective_placeholder=Enter "language upgrade" or "sql conversion"
codemodernizer.chat.message.download_failed_client_instructions_expired=Your transformation is not available anymore. Your code and transformation summary are deleted 24 hours after the transformation completes. Please try starting the transformation again.
codemodernizer.chat.message.download_failed_invalid_artifact=Sorry, I was unable to find your {0}. Artifacts are deleted after 24 hours. Please try starting the transformation again.
codemodernizer.chat.message.download_failed_other=Sorry, I ran into an issue while trying to download your {0}. Please try again. {1}
codemodernizer.chat.message.download_failed_ssl=Sorry, I couldn''t download your {0} because of an issue with your certificate. Please make sure all your certificates for your proxy client have been set up correctly for your IDE.
codemodernizer.chat.message.download_failed_wildcard=Sorry, I couldn''t download your {0} because of an issue with your proxy client. Please check your IDE proxy settings and remove any wildcard (*) references, then restart your IDE.
codemodernizer.chat.message.enter_jdk_name=Enter the name of the {0} you are using. You can find the name in File > Project Structure > Platform Settings > SDKs. If you do not see the name of your JDK in the SDK settings, add your JDK, and then return to this chat and enter the name here.
codemodernizer.chat.message.enter_jdk_name_error=I could not find "{0}" in File > Project Structure > Platform Settings > SDKs. Please add the target JDK there and try again.
codemodernizer.chat.message.error_request=Request failed
codemodernizer.chat.message.follow_up.new_transformation=Start a new transformation
codemodernizer.chat.message.hil.cannot_resume=I ran into an issue trying to resume your transformation.
codemodernizer.chat.message.hil.continue_after_error=I'll continue upgrading your module. When I'm done, you can review the dependency error in the Transformation summary.
codemodernizer.chat.message.hil.dependency_choose_version=I can replace this dependency with a newer version. Choose which version I should use:
codemodernizer.chat.message.hil.dependency_latest_incremental=\n\nLatest incremental version: {0}
codemodernizer.chat.message.hil.dependency_latest_major=\n\nLatest major version: {0}
codemodernizer.chat.message.hil.dependency_latest_minor=\n\nLatest minor version: {0}
codemodernizer.chat.message.hil.dependency_summary=I found {0} other dependency versions that are more recent than the dependency in your code that''s causing an error ({1}).
codemodernizer.chat.message.hil.error.cancel_dependency_search=I cancelled the local dependency search.
codemodernizer.chat.message.hil.error.cancel_upload=I cancelled the dependency upload.
codemodernizer.chat.message.hil.error.cannot_download_artifact=I ran into an error trying to download the dependency information.
codemodernizer.chat.message.hil.error.cannot_upload=I'm sorry, I wasn't able to upload the dependency you chose.
codemodernizer.chat.message.hil.error.no_other_versions_found=I couldn''t find any other versions of this dependency in your local Maven repository. Try transforming the **{0}** dependency to make it compatible with your target Java version, and then try transforming this module again.
codemodernizer.chat.message.hil.pom_snippet_title=Here is the dependency causing the issue:
codemodernizer.chat.message.hil.resumed=I received your target version dependency. I'll continue transforming your code. You can monitor progress in the Transformation Hub.
codemodernizer.chat.message.hil.searching=I'm searching for other dependency versions available in your Maven repository...
codemodernizer.chat.message.hil.start_message=I was not able to upgrade all dependencies. To resolve it, I'll try to find an updated dependency in your local Maven repository. I'll need additional information from you to continue.
codemodernizer.chat.message.hil.trying_resume=Trying to resume transformation with your selected version.
codemodernizer.chat.message.hil.user_rejected=I'll continue upgrading your module. When I'm done, you can review the dependency error in the Transformation summary.
codemodernizer.chat.message.local_build_begin=I'm building your module. This can take up to 10 minutes, depending on the size of your module.
codemodernizer.chat.message.local_build_failed=Sorry, I couldn't run the Maven clean install command to build your module.
codemodernizer.chat.message.local_build_success=I was able to build your module and will start uploading your code.
codemodernizer.chat.message.result.fail=Sorry, I ran into an issue during the transformation. Please try again.
codemodernizer.chat.message.result.fail_initial_build=I am having trouble building your project in the secure build environment and couldn't complete the transformation.
codemodernizer.chat.message.result.fail_initial_build_no_build_log=I am having trouble building your project in the secure build environment: {0}.
codemodernizer.chat.message.result.fail_with_known_reason=Sorry, I couldn''t complete the transformation. {0}
codemodernizer.chat.message.result.partially_success=I transformed part of your code. You can review the diff to see my proposed changes and accept or reject them. The transformation summary has details about the files I updated and the errors that prevented a complete transformation.
codemodernizer.chat.message.result.success=I successfully completed your transformation. You can review the diff to see my proposed changes and accept or reject them. The transformation summary has details about the changes I'm proposing.
codemodernizer.chat.message.result.zip_too_large=Sorry, your project size exceeds the Amazon Q Code Transformation upload limit of 2GB.
codemodernizer.chat.message.resume_ongoing=I'm still transforming your code. It can take 10 to 30 minutes to upgrade your code, depending on the size of your module. To monitor progress, go to the Transformation Hub.
codemodernizer.chat.message.skip_tests=I will build your project using `mvn clean test` by default. If you would like me to build your project without running unit tests, I will use `mvn clean test-compile`.
codemodernizer.chat.message.skip_tests_form.response=Okay, I will {0} when building your module.
codemodernizer.chat.message.skip_tests_form.run_tests=Run unit tests
codemodernizer.chat.message.skip_tests_form.skip=Skip unit tests
codemodernizer.chat.message.sql_metadata_success=I found the following source database, target database, and host based on the schema conversion metadata you provided:
codemodernizer.chat.message.sql_module_schema_prompt=To continue, choose the module and schema for this transformation.
codemodernizer.chat.message.transform_begin=I'm starting to transform your code. It can take 10 to 30 minutes to upgrade your code, depending on the size of your module. To monitor progress, go to the Transformation Hub.
codemodernizer.chat.message.transform_cancelled_by_user=I cancelled your transformation. If you want to start another transformation, choose **Start a new transformation**.
codemodernizer.chat.message.transform_failed=I could not complete the transformation. {0}
codemodernizer.chat.message.transform_in_progress=If I run into any issues, I might pause the transformation to get input from you on how to proceed.
codemodernizer.chat.message.transform_stopped_by_user=I stopped your transformation. If you want to start another transformation, choose **Start a new transformation**.
codemodernizer.chat.message.transform_stopping=I'm stopping your transformation...
codemodernizer.chat.message.upload_failed_connection_refused=Sorry, I couldn't upload your project to begin the transformation. Please check your network connectivity or firewall configuration, and then try again.
codemodernizer.chat.message.upload_failed_http_error=Sorry, I couldn''t upload your project to start the transformation. You can investigate the issue with the HTTP Status Code: {0}
codemodernizer.chat.message.upload_failed_other=Sorry, I was unable to upload your project due to an unexpected failure. {0}
codemodernizer.chat.message.upload_failed_ssl_error=Sorry, I was unable to upload your project. This might have been caused by your IDE not trusting the certificate of your HTTP proxy. Ensure all certificates for your proxy client have been configured in your IDE, and then retry transformation.
codemodernizer.chat.message.upload_failed_url_expired=Sorry, I couldn't upload your project to begin the transformation. The Amazon S3 pre-signed URL used to upload your code expired after 30 minutes. This might have been caused by delays introduced by intermediate services in your network infrastructure.\n\nCheck your network configuration for services that might be causing delays. If the issue persists, you might need to allow list the following Amazon S3 bucket: 'amazonq-code-transformation-us-east-1-c6160f047e0.s3.amazonaws.com'.
codemodernizer.chat.message.validation.check_eligible_modules=Checking for eligible modules...
codemodernizer.chat.message.validation.check_passed=I can upgrade your Java module. To start the transformation, I need some information from you. Choose the module you want to upgrade and the target code version to upgrade to. Then, choose **Confirm**.\n\nIf you do not see the module you want to transform, you might need to configure your project so that I can find it. Go to File and choose Project Structure. In the Projects tab, set the correct project JDK and language level. In the Modules tab, set the correct module JDK and language level.
codemodernizer.chat.message.validation.error.downgrade_attempt=I can't transform a project from Java 21 to Java 17, but I can upgrade Java 21 code with up-to-date libraries and other dependencies. Try again with a supported language upgrade.
codemodernizer.chat.message.validation.error.invalid_sct=It looks like the .sct file you provided isn't valid. Make sure that you've uploaded the .zip file you retrieved from your schema conversion in AWS DMS.
codemodernizer.chat.message.validation.error.invalid_source_db=I can only convert SQL for migrations from an Oracle source database. The provided .sct file indicates another source database for this migration.
codemodernizer.chat.message.validation.error.invalid_target_db=I can only convert SQL for migrations to Aurora PostgreSQL or Amazon RDS for PostgreSQL target databases. The provided .sct file indicates another target database for this migration.
codemodernizer.chat.message.validation.error.missing_sct_file=An .sct file is required for transformation. Make sure that you've uploaded the .zip file you retrieved from your schema conversion in AWS DMS.
codemodernizer.chat.message.validation.error.more_info=For more information, see the [Amazon Q documentation]({0}).
codemodernizer.chat.message.validation.error.no_java_project=Sorry, I could not find an open Java module with embedded Oracle SQL statements. Make sure you have a Java module open that has at least 1 content root.
codemodernizer.chat.message.validation.error.other=I couldn't find a module that I can upgrade. Currently, I support Java 8, Java 11, Java 17, and Java 21 projects built on Maven. Make sure your project is open in the IDE. If you have a Java 8, Java 11, Java 17, or Java 21 module in your workspace, you might need to configure your project so that I can find it. Go to File and choose Project Structure. In the Projects tab, set the correct project JDK and the correct language level. In the Modules tab, set the correct module JDK and language level.
codemodernizer.chat.message.validation.error.unsupported_module=I couldn't find a module that I can upgrade. Currently, I support Java 8, Java 11, Java 17, and Java 21 projects built on Maven. Make sure your project is open in the IDE. If you have a Java 8, Java 11, Java 17, or Java 21 in your workspace, you might need to configure your project so that I can find it. Go to File and choose Project Structure. In the Projects tab, set the correct project JDK and the correct language level. In the Modules tab, set the correct module JDK and language level.
codemodernizer.chat.message.validation.no_jdk=I couldn't build your project with your current JDK configuration. To update your JDK, go to File and choose Project Structure. In the Projects tab, set the correct project JDK in the SDK field. In the Modules tab, set the correct module JDK in the SDK field. In Maven Runner settings, set the correct JDK in the JRE field.
codemodernizer.chat.prompt.label.dependency_current_version=Current version
codemodernizer.chat.prompt.label.dependency_name=Dependency name
codemodernizer.chat.prompt.label.dependency_selected_version=Target version
codemodernizer.chat.prompt.label.module=Module
codemodernizer.chat.prompt.label.target_version=Target JDK version
codemodernizer.chat.prompt.stop_transform=Stop transformation
codemodernizer.chat.prompt.title.dependency_details=Dependency details
codemodernizer.chat.prompt.title.details=Transformation details
codemodernizer.explorer.show_job_status=Show job status
codemodernizer.explorer.show_job_status_description=View job status
codemodernizer.explorer.show_transformation_plan_title=View your code transformation plan
codemodernizer.explorer.show_transformation_status=Show transformation status
codemodernizer.explorer.show_transformation_status_description=View transformation status
codemodernizer.explorer.show_transformation_summary_title=View your code transformation summary
codemodernizer.explorer.stop_migration_job=Stop Transformation
codemodernizer.file.invalid_pom_version=Amazon Q experienced an issue upgrading this dependency version. Use Amazon Q chat to upgrade the version of this dependency to a version compatible with your target Java version.
codemodernizer.manager.job_ongoing_content=Amazon Q is still transforming your code. To see the current status of the transformation, go to the Transformation Hub.
codemodernizer.manager.job_ongoing_title=Code Transformation ongoing
codemodernizer.migration_plan.body.info.action_column=Action
codemodernizer.migration_plan.body.info.appendix_title=Appendix
codemodernizer.migration_plan.body.info.changed_files_column=Files to be changed
codemodernizer.migration_plan.body.info.current_version_column=Current version
codemodernizer.migration_plan.body.info.dependency_name_column=Dependency
codemodernizer.migration_plan.body.info.dependency_replace_message=Dependencies to be replaced
codemodernizer.migration_plan.body.info.deprecated_code_column=Deprecated code
codemodernizer.migration_plan.body.info.deprecated_code_message=Deprecated code instances to be replaced
codemodernizer.migration_plan.body.info.file_column=File
codemodernizer.migration_plan.body.info.files_changed_message=Files to be changed
codemodernizer.migration_plan.body.info.job_statistic_message=<html><body style="line-height:2; font-family: Arial, sans-serif; font-size: 14;">{0}: {1}</body></html>
codemodernizer.migration_plan.body.info.lines_of_code_message=Lines of code in your application
codemodernizer.migration_plan.body.info.target_version_column=Target version
codemodernizer.migration_plan.body.steps_intro_subtitle=<html><body style="line-height:2; font-family: Arial, sans-serif; font-size: 14; font-style: italic">Amazon Q will use the proposed changes as guidance during the transformation. The final code updates might differ from this plan. <a href="https://docs.aws.amazon.com/amazonq/latest/qdeveloper-ug/code-transformation.html">Read more.</a></body></html>
codemodernizer.migration_plan.body.steps_intro_title=Planned transformation changes
codemodernizer.migration_plan.body.steps_name=<html><body>{0}</body></html>
codemodernizer.migration_plan.body.steps_scroll_top=<html><body style="font-family: Arial, sans-serif; font-size: 14;"><a href="#top">Scroll to top</a></body></html>
codemodernizer.migration_plan.header.awsq=<html><body style="line-height:2; font-family: Arial, sans-serif; font-size: 14;">Amazon Q reviewed your code and generated a transformation plan. Amazon Q will suggest code changes according to the plan, and you can review the updated code before accepting changes to your files.</body></html>
codemodernizer.migration_plan.header.billing_text=<html><body style="line-height:2; font-family: Arial, sans-serif; font-size: 14;"><br>{0} lines of code were submitted for transformation. If you reach the quota for lines of code included in your subscription, you will be charged ${1} for each additional line of code. You might be charged up to ${2} for this transformation. To avoid being charged, stop the transformation job before it completes. For more information on pricing and quotas, see <a href="https://aws.amazon.com/q/developer/pricing/">Amazon Q Developer pricing</a>.</p>
codemodernizer.migration_plan.header.description=Plan to transform your module
codemodernizer.migration_plan.header.title=Code Transformation plan by Amazon Q
codemodernizer.migration_plan.substeps.description_completed=Build completed
codemodernizer.migration_plan.substeps.description_failed=Build failed
codemodernizer.migration_plan.substeps.description_stopped=Job is stopped
codemodernizer.migration_summary.header.title=Transformation summary
codemodernizer.notification.info.download.started.content=Downloading the updated code
codemodernizer.notification.info.download.started.title=Download Started
codemodernizer.notification.info.modernize_complete.content=Amazon Q finished the transformation. You can review the diff to see the proposed changes and accept or reject them. The transformation summary has details about the files that were updated.
codemodernizer.notification.info.modernize_complete.title=Transform Complete
codemodernizer.notification.info.modernize_complete.view_summary=View transformation summary
codemodernizer.notification.info.modernize_failed.connection_failed=Amazon Q could not complete the transformation. Try starting the transformation again. {0}
codemodernizer.notification.info.modernize_failed.title=Transformation failed
codemodernizer.notification.info.modernize_failed.unknown_failure_reason=Unknown failure reason
codemodernizer.notification.info.modernize_ongoing.view_status=View status
codemodernizer.notification.info.modernize_partial_complete.content=Amazon Q transformed part of your code. You can review the diff to see my proposed changes and accept or reject them. The transformation summary has details about the files I updated and the errors that prevented a complete transformation.
codemodernizer.notification.info.modernize_partial_complete.title=Transformation partially successful!
codemodernizer.notification.info.transformation_resume.content=Amazon Q was unable to resume polling for the job you started before closing the module.
codemodernizer.notification.info.transformation_resume.title=Unable to resume polling for job updates.
codemodernizer.notification.info.transformation_start_stopping.as_no_response=Amazon Q could not stop the transformation.
codemodernizer.notification.info.transformation_start_stopping.content=Amazon Q is stopping your transformation. This might take a few seconds.
codemodernizer.notification.info.transformation_start_stopping.failed_content=Amazon Q could not stop the transformation.
codemodernizer.notification.info.transformation_start_stopping.failed_title=Error stopping transformation
codemodernizer.notification.info.transformation_start_stopping.title=Transformation stopping
codemodernizer.notification.info.transformation_stop.content=You cancelled the transformation. To start a new transformation, return to the Q - Transform chat tab.
codemodernizer.notification.info.transformation_stop.title=Transformation cancelled
codemodernizer.notification.info.view_troubleshooting_guide=View troubleshooting guide
codemodernizer.notification.warn.action.reauthenticate=Reauthenticate
codemodernizer.notification.warn.download_failed_client_instructions_expired=Your transformation is not available anymore. Your code and transformation summary are deleted 24 hours after the transformation completes. Please try starting the transformation again.
codemodernizer.notification.warn.download_failed_expired_credentials.content=Unable to download results as your credentials expired, please reauthenticate to Q and try again.
codemodernizer.notification.warn.download_failed_invalid_artifact=Amazon Q was unable to find your {0}. Artifacts are deleted after 24 hours. Please try starting the transformation again.
codemodernizer.notification.warn.download_failed_other.content=Amazon Q ran into an issue while trying to download your {0}. Please try again. {1}
codemodernizer.notification.warn.download_failed_ssl.content=Please make sure all your certificates for your proxy client have been set up correctly for your IDE.
codemodernizer.notification.warn.download_failed_wildcard.content=Check your IDE proxy settings and remove any wildcard (*) references, and then try viewing the diff again.
codemodernizer.notification.warn.expired_credentials.content=Unable to check transformation status as your credentials expired. Try signing out of the Amazon Q plugin and signing in again.
codemodernizer.notification.warn.expired_credentials.title=Your connection to Q has expired
codemodernizer.notification.warn.invalid_project.description.reason.missing_content_roots=None of your open modules are supported for code transformation with Amazon Q. Amazon Q can upgrade Java 8, Java 11, Java 17, and Java 21 projects built on Maven, with content roots configured.
codemodernizer.notification.warn.invalid_project.description.reason.not_logged_in=Amazon Q cannot start the transformation as you are not logged in with Identity Center or Builder ID. Also ensure that you are not using IntelliJ version 232.8660.185 and that you are not developing on a remote host (uncommon).
codemodernizer.notification.warn.invalid_project.description.reason.remote_backend=None of your open modules are supported for code transformation with Amazon Q. Amazon Q cannot transform modules running on a remote host.
codemodernizer.notification.warn.maven_failed.content=Amazon Q could not run the Maven clean install command to build your module.
codemodernizer.notification.warn.maven_failed.title=Amazon Q Code Transform unable to zip dependencies
codemodernizer.notification.warn.on_resume.unknown_status_response.content=We received data from Amazon Q in a format that the plugin cannot handle. You may need to update the plugin and then try again.
codemodernizer.notification.warn.on_resume.unknown_status_response.title=Unable to resume job
codemodernizer.notification.warn.submit_feedback=Submit feedback
codemodernizer.notification.warn.unable_to_start_job=Amazon Q could not begin the transformation. Try starting the transformation again. {0}
codemodernizer.notification.warn.unknown_start_failure=Amazon Q could not begin the transformation. Try starting the transformation again.
codemodernizer.notification.warn.unknown_status_response=Amazon Q could not complete the transformation. Try starting the transformation again.
codemodernizer.notification.warn.upload_failed=Amazon Q could not upload your module. Try starting the transformation again. {0}
codemodernizer.notification.warn.upload_failed_expired_credentials.content=Unable to upload results as your credentials expired, please reauthenticate to Q and try again.
codemodernizer.notification.warn.validation.no_jdk=Amazon Q couldn't build your module with your JDK configuration. Go to File and choose Project Structure to update your project SDK and module SDK.
codemodernizer.notification.warn.view_build_log_failed.content=Unable to display the failure build log due to an error.
codemodernizer.notification.warn.view_build_log_failed.title=Unable to display failure build log
codemodernizer.notification.warn.view_diff_failed.content=Amazon Q could not download and parse the diff with your upgraded code. {0}
codemodernizer.notification.warn.view_diff_failed.title=Failed to download upgraded code
codemodernizer.notification.warn.view_summary_failed.content=Unable to display the transformation summary due to an error.
codemodernizer.notification.warn.view_summary_failed.title=Unable to display transformation summary
codemodernizer.notification.warn.zip_creation_failed=Amazon Q could not zip the selected module and begin the transformation. Try starting the transformation again. {0}
codemodernizer.notification.warn.zip_creation_failed.reasons.unknown=An unexpected error occurred
codemodernizer.notification.warn.zip_too_large.content=Sorry, your module size exceeds the Amazon Q Code Transformation upload limit of 2GB.
codemodernizer.notification.warn.zip_too_large.title=Module size exceeds limit
codemodernizer.toolwindow.banner.action.feedback=Provide Feedback
codemodernizer.toolwindow.banner.action.plan=View transformation plan
codemodernizer.toolwindow.banner.action.summary=View transformation summary
codemodernizer.toolwindow.banner.job_failed_to_start=Job could not be started.
codemodernizer.toolwindow.banner.job_failed_while_running=Job failed.
codemodernizer.toolwindow.banner.job_is_running=Job is running.
codemodernizer.toolwindow.banner.job_is_stopped=Job is stopped.
codemodernizer.toolwindow.banner.job_starting=Job is starting.
codemodernizer.toolwindow.banner.no_ongoing_job=No job ongoing.
codemodernizer.toolwindow.banner.run_scan_complete=All steps of transformation successful
codemodernizer.toolwindow.banner.run_scan_info=Select 'Transform' in toolbar to upgrade this package.
codemodernizer.toolwindow.job_status.header=Transformation status
codemodernizer.toolwindow.label=Transformation Hub - {0}
codemodernizer.toolwindow.label_no_job=Transformation Hub
codemodernizer.toolwindow.problems_mvn_window_not_found=Unable to display Code Transform results as the Maven window cannot be fetched.
codemodernizer.toolwindow.problems_window_not_found=Unable to display Code Transform results as the Problems View tool window cannot be fetched.
codemodernizer.toolwindow.progress.building=Build uploaded code in secure build environment
codemodernizer.toolwindow.progress.parent=Transformation progress
codemodernizer.toolwindow.progress.planning=Generate transformation plan
codemodernizer.toolwindow.progress.transforming=Transform your code
codemodernizer.toolwindow.progress.uploading=Uploading your code
codemodernizer.toolwindow.scan_display=Transformation details
codemodernizer.toolwindow.scan_in_progress=Amazon Q is scanning the project files and getting ready to start the job. To start the job, Amazon Q needs to upload the project artifacts. Once that's done, Q can start the transformation job. The estimated time for this operation ranges from a few seconds to several minutes.
codemodernizer.toolwindow.scan_in_progress.accepted=Files have been uploaded to Amazon Q, transformation job has been accepted and is preparing to start.
codemodernizer.toolwindow.scan_in_progress.building=Amazon Q is building your code using Java {0} in a secure build environment.
codemodernizer.toolwindow.scan_in_progress.failed=The step failed, fetching additional details...
codemodernizer.toolwindow.scan_in_progress.planning=Amazon Q is analyzing your code in order to generate a transformation plan.
codemodernizer.toolwindow.scan_in_progress.stopping=Stopping the job...
codemodernizer.toolwindow.scan_in_progress.transforming=Amazon Q is transforming your code.
codemodernizer.toolwindow.stop_scan=Stop job
codemodernizer.toolwindow.table.header.date=Date
codemodernizer.toolwindow.table.header.job_id=Job ID
codemodernizer.toolwindow.table.header.module_name=Module name
codemodernizer.toolwindow.table.header.run_length=Job running time
codemodernizer.toolwindow.table.header.status=Status
codemodernizer.toolwindow.transformation.progress.header=Transformation progress
codemodernizer.toolwindow.transformation.progress.job_id=Job ID: {0}
codemodernizer.toolwindow.transformation.progress.running_time=Running time: {0}
codescan.chat.message.button.fileScan=Review active file
codescan.chat.message.button.openIssues=View in Code Issues Panel
codescan.chat.message.button.projectScan=Review project
codescan.chat.message.error_request=Request failed
codescan.chat.message.not_git_repo=Your workspace is not in a git repository. I'll review your project files for security issues, and your in-flight changes for code quality issues.
codescan.chat.message.project_scan_failed=Sorry, I ran into an issue during the review. Please try again.
codescan.chat.message.scan_begin_file=Okay, I'm reviewing your file for code issues.
codescan.chat.message.scan_begin_project=Okay, I'm reviewing your project for code issues.
codescan.chat.message.scan_begin_wait_time=This may take a few minutes. I'll share updates here as I work on this.
codescan.chat.message.scan_file_in_progress=File review is in progress...
codescan.chat.message.scan_project_in_progress=Project review is in progress...
codescan.chat.message.scan_step_1=Initiating code review.
codescan.chat.message.scan_step_2=Waiting for review to finish.
codescan.chat.message.scan_step_3=Processing review results.
codescan.chat.new_scan.input.message=Which type of review would you like to run?
codescan.chat.placeholder.scan_in_progress=Reviewing code issues...
codescan.chat.placeholder.waiting_for_inputs=Waiting on your inputs...
codewhisperer.actions.connect_github.title=Connect with Us on GitHub
codewhisperer.actions.open_settings.title=Open Settings
codewhisperer.actions.send_feedback.title=Send Feedback
codewhisperer.actions.view_documentation.title=View Documentation
codewhisperer.codefix.code_fix_job_timed_out=Amazon Q: Timed out generating code fix
codewhisperer.codefix.create_code_fix_error=Amazon Q: Failed to generate fix for the issue
codewhisperer.codefix.invalid_zip_error=Amazon Q: Failed to create valid zip
codewhisperer.codescan.apply_fix_button_label=Apply fix
codewhisperer.codescan.apply_fix_button_tooltip=Apply suggested fix
codewhisperer.codescan.build_artifacts_not_found=<html>Cannot find build artifacts for the project. Try rebuilding the Java project in IDE or specify compilation output path in <b>File | Project Structure... | Project | Compiler output:</b></html>
codewhisperer.codescan.cancelled_by_user_exception=Code review job cancelled by user.
codewhisperer.codescan.cannot_read_file=Amazon Q encountered an error while parsing a file.
codewhisperer.codescan.clear_filters=Clear Filters
codewhisperer.codescan.cwe_label=Common Weakness Enumeration (CWE)
codewhisperer.codescan.detector_library_label=Detector library
codewhisperer.codescan.explain_button_label=Explain
codewhisperer.codescan.file_ext_not_supported=File extension {0} is not supported for the Amazon Q Code Review feature. Please try again with a valid file format - java, python, javascript, typescript, csharp, yaml, json, tf, hcl, ruby, go.
codewhisperer.codescan.file_name_issues_count=<html><body> {0} <font color="{3}">  {1} {2, choice, 1#1 issue|2#{2,number} issues}</font></body></html>
codewhisperer.codescan.file_not_found=For file path {0} with error message: {0}
codewhisperer.codescan.file_path_label=File Path
codewhisperer.codescan.file_too_large=Amazon Q: The selected file exceeds the input artifact limit. Try again with a smaller file. For more information about review limits, see the Amazon Q documentation. 
codewhisperer.codescan.file_too_large_telemetry=Payload size limit reached
codewhisperer.codescan.fix_applied_fail=Apply fix command failed. {0}
codewhisperer.codescan.fix_available_label=Code fix available
codewhisperer.codescan.fix_button_label=Fix with Q
codewhisperer.codescan.generate_fix_button_label=Generate Fix
codewhisperer.codescan.ignore_all_button=Ignore All
codewhisperer.codescan.ignore_button=Ignore
codewhisperer.codescan.invalid_source_zip_telemetry=Failed to create valid source zip.
codewhisperer.codescan.java_module_not_found=Java plugin is required for reviewing Java files, install Java plugin or perform the code review in Intellij Idea instead.
codewhisperer.codescan.no_file_open=Amazon Q: No file is open in an active editor. Open a file to start a Code Review.
codewhisperer.codescan.no_file_open_telemetry=Open a valid file to review.
codewhisperer.codescan.problems_window_not_found=Unable to display Code Review results as the Problems View tool window cannot be fetched.
codewhisperer.codescan.quota_exceeded=You've reached the monthly quota for Amazon Q Developer's agent capabilities. You can try again next month. For more information on usage limits, see the Amazon Q Developer pricing page.
codewhisperer.codescan.regenerate_fix_button_label=Regenerate Fix
codewhisperer.codescan.run_scan=<html><body>Full Project Scan is now /review! <font color="{0}">Open in Chat Panel</font></body></html>
codewhisperer.codescan.run_scan_complete=<html><body> Code Review completed for {0, choice, 1#1 file|2#{0,number} files}. {1, choice, 0#No issues|1#1 issue|2#{1,number} issues} found in {2}. <font color="{3}">  Last Run {4} </font></body></html>
codewhisperer.codescan.run_scan_error=Amazon Q encountered an error while reviewing for code issues. Please try again later.
codewhisperer.codescan.run_scan_error_telemetry=Code Review failed.
codewhisperer.codescan.run_scan_info=Enter /review in Amazon Q Chat Panel to run code reviews.
codewhisperer.codescan.scan_complete_count=- {0}: `{1, choice, 1#{1,number} issue|2#{1,number} issues}`
codewhisperer.codescan.scan_complete_file=Reviewing your File is complete. Here's what I found:
codewhisperer.codescan.scan_complete_project=Reviewing your Project is complete. Here's what I found:
codewhisperer.codescan.scan_display=Amazon Q Code Issues
codewhisperer.codescan.scan_display_with_issues=<html><body>Amazon Q Code Issues <font color="{1}"> {0} </font></body></html>
codewhisperer.codescan.scan_in_progress=Code review in progress...
codewhisperer.codescan.scan_recommendation=<html><body> {0} <font color="{2}">  {1}</font></body></html>
codewhisperer.codescan.scan_recommendation_invalid=<html><body> {0} <font color="{2}">  {1} [No longer valid: Re-run the review to validate the fix]</font></body></html>
codewhisperer.codescan.scan_recommendation_invalid.tooltip_text=No longer valid. Re-run the review to validate the fix.
codewhisperer.codescan.scan_results_hidden_by_filters=All code review results are hidden by current filters.
codewhisperer.codescan.scan_timed_out=Code Review failed. Amazon Q timed out.
codewhisperer.codescan.scanned_files_heading=<html><b>  {0} files were reviewed during the last code review. </b></html>
codewhisperer.codescan.severity_issues_count=<html><body> {0} <font color="{2}"> {1, choice, 1#{1,number} issue|2#{1,number} issues}</font></body></html>
codewhisperer.codescan.stop_scan=Stop Code Review
codewhisperer.codescan.stop_scan_confirm_button=Stop review
codewhisperer.codescan.stop_scan_confirm_message=Are you sure you want to stop ongoing code review? This review will be counted as one complete review towards your monthly code review limits.
codewhisperer.codescan.stopping_scan=Stopping Code Review...
codewhisperer.codescan.suggested_fix_description=Why are we recommending this?
codewhisperer.codescan.suggested_fix_label=Suggested code fix preview
codewhisperer.codescan.unsupported_language_error=Amazon Q: Project does not contain valid files to review
codewhisperer.codescan.unsupported_language_error_telemetry=Project does not contain valid files to review
codewhisperer.codescan.upload_to_s3_failed=Amazon Q is unable to upload your project artifacts to Amazon S3 for code reviews. For more information, see the Amazon Q documentation.
codewhisperer.codescan.view_scanned_files=View {0} reviewed files
codewhisperer.credential.login.dialog.exception.cancel_login=Login cancelled
codewhisperer.credential.login.dialog.ok_button=Connect
codewhisperer.credential.login.dialog.prompt=Select a connection option to start using Amazon Q
codewhisperer.credential.login.exception.general=Ran into unknown error: {0}
codewhisperer.credential.login.exception.general.oidc=Fail to fetch credential
codewhisperer.credential.login.exception.invalid_grant=Access denied
codewhisperer.credential.login.exception.invalid_input=Invalid start url, region, or scopes
codewhisperer.credential.login.exception.io=Unable to access SSO disk cache: {0}
codewhisperer.custom.dialog.customization.no_description=No description provided
codewhisperer.custom.dialog.model.default.comment=Receive suggestions from Amazon Q base model.
codewhisperer.custom.dialog.ok_button.text=Select
codewhisperer.custom.dialog.option.customization=Customization
codewhisperer.custom.dialog.option.customization.description=Receive Amazon Q suggestions based on your company's codebase.
codewhisperer.custom.dialog.option.customization.description.no_customization=Contact your administrator for access to Amazon Q customizations. After you have access, they will be displayed in the dropdown below. <a href="{0}"> Learn more (external link)</a>
codewhisperer.custom.dialog.option.default=Amazon Q foundation (Default)
codewhisperer.custom.dialog.option.no_data=No customizations available
codewhisperer.custom.dialog.panel.title=Select an Amazon Q customization
codewhisperer.custom.dialog.title=Amazon Q Customization
codewhisperer.experiment=Amazon Q
codewhisperer.explorer.code_reference.open=Open Code Reference Log
codewhisperer.explorer.customization.select=Select Customization
codewhisperer.explorer.enable=Start
codewhisperer.explorer.enabled=\   Enabled
codewhisperer.explorer.learn=Learn
codewhisperer.explorer.node.dismiss=Dismiss
codewhisperer.explorer.node.install_q=Install the Amazon Q Plugin
codewhisperer.explorer.pause_auto=Pause Auto-Suggestions
codewhisperer.explorer.pause_auto_scans=Pause Auto-Reviews
codewhisperer.explorer.paused=\   Paused
codewhisperer.explorer.reconnect=Reconnect
codewhisperer.explorer.resume_auto=Resume Auto-Suggestions
codewhisperer.explorer.resume_auto_scans=Resume Auto-Reviews
codewhisperer.explorer.tooltip.comment=Start with auto-suggestions and find more features here!
codewhisperer.explorer.tooltip.title=Get started with Amazon Q
codewhisperer.explorer.usage_limit_hit=\   Free tier limit met, paused until {0}
codewhisperer.explorer.what_is=Learn More about Amazon Q
codewhisperer.gettingstarted.panel.comment=Build, maintain and transform applications<br>using generative AI.
codewhisperer.gettingstarted.panel.learn_more=Learn more
codewhisperer.gettingstarted.panel.learn_more.with.q=Learn more about <a href = "https://aws.amazon.com/q/">Amazon Q</a> and <a href ="https://aws.amazon.com/codewhisperer">Codewhisperer  </a>
codewhisperer.gettingstarted.panel.licence_comment=Already have a license?
codewhisperer.gettingstarted.panel.login_button=Use for free, no AWS account required
codewhisperer.inline.accept=Accept Amazon Q Suggestion
codewhisperer.inline.force.accept=Force Accept Suggestion
codewhisperer.inline.navigate.next=Navigate to Next Amazon Q Suggestion
codewhisperer.inline.navigate.previous=Navigate to Previous Amazon Q Suggestion
codewhisperer.inline.settings.tab_priority.choice.amazonq=Amazon Q
codewhisperer.inline.settings.tab_priority.choice.jetbrains=JetBrains(IntelliSense)
codewhisperer.inline.settings.tab_priority.notification.text=You can adjust the behavior of the Tab key to prioritize accepting either Amazon Q suggestions or JetBrains(IntelliSense) suggestions.
codewhisperer.inline.settings.tab_priority.prefix=When both suggestions show, press Tab to accept
codewhisperer.inline.settings.tab_priority.suffix= suggestions first
codewhisperer.language.error={0} is currently not supported by Amazon Q
codewhisperer.learn_page.banner.dismiss=Dismiss
codewhisperer.learn_page.banner.message.new_user=You can always return to this page by clicking "Learn" in the Amazon Q status bar menu.
codewhisperer.learn_page.examples.description.part_1=Amazon Q inline suggestions support 
codewhisperer.learn_page.examples.description.part_2=15 programming languages
codewhisperer.learn_page.examples.description.part_3=, including Java, JavaScript, Python, Go, and more.
codewhisperer.learn_page.examples.tasks.button=Try example
codewhisperer.learn_page.examples.tasks.description_1=Generate code suggestions as you type
codewhisperer.learn_page.examples.tasks.description_2.mac=Generate code suggestions manually using Option + C
codewhisperer.learn_page.examples.tasks.description_2.win=Generate code suggestions manually using Alt + C
codewhisperer.learn_page.examples.tasks.description_3=Generate unit test cases
codewhisperer.learn_page.examples.title=Generate code suggestions with examples
codewhisperer.learn_page.header.description=An AI assistant that reimagines your experience across the entire development lifecycle
codewhisperer.learn_page.header.title=Amazon Q inline code suggestions
codewhisperer.loading_licenses=Loading Licenses Info
codewhisperer.notification.accountless.error.action.connect=Connect with AWS
codewhisperer.notification.accountless.warn.action.connect=Connect with AWS to Continue
codewhisperer.notification.custom.new_customization=You have access to new Amazon Q customizations
codewhisperer.notification.custom.not_available=Selected Amazon Q customization is not available. Contact your administrator. Your instance of Amazon Q is using the foundation model.
codewhisperer.notification.custom.simple.button.got_it=Got it
codewhisperer.notification.custom.simple.button.select_another_customization=Select another customization
codewhisperer.notification.custom.simple.button.select_customization=Select customization
codewhisperer.notification.remote.ide_unsupported.message=Please update your IDE backend to a 2023.3 or later version to continue using Amazon Q inline suggestions.
codewhisperer.notification.remote.ide_unsupported.title=Amazon Q inline suggestion not supported in this IDE version
codewhisperer.notification.usage_limit.codescan.warn.content=Amazon Q: You have reached the monthly limit for project reviews.
codewhisperer.notification.usage_limit.codesuggestion.warn.content=You have reached the monthly fair use limit of code recommendations.
codewhisperer.popup.button.accept=<html><center>&nbsp;Insert&nbsp;Code&nbsp;<br/><font color="{0}">\u21E5</font></center></html>
codewhisperer.popup.button.next=<html><center>Next<br/><font color="{0}">{1}</font></center></html>
codewhisperer.popup.button.prev=<html><center>Previous<br/><font color="{0}">{1}</font></center></html>
codewhisperer.popup.import_info=<html><p style="margin: 0; width: 210px">If you insert code, "{0}"{2, choice, 0#|1# and {1} other import|2# and {1} other imports} will also be added.</p></html>
codewhisperer.popup.no_recommendations=No Amazon Q recommendations available at this point
codewhisperer.popup.pagination_info=Loading additional Amazon Q suggestions...
codewhisperer.popup.recommendation_info=<html><font color="{2}">Suggestion</font> {0} of {1} <font color="{2}">from Amazon Q</font></html>
codewhisperer.popup.reference.license_info.prefix=<html><font color="{0}">&#8226;</font>&nbsp;Reference code under&nbsp;</html>
codewhisperer.popup.reference.panel_link=View Log
codewhisperer.statusbar.display_name=Amazon Q
codewhisperer.statusbar.popup.title=Reconnect to Amazon Q?
codewhisperer.statusbar.sub_menu.connect_help.title=Connect / Help
codewhisperer.statusbar.sub_menu.inline.title=Inline Suggestions
codewhisperer.statusbar.sub_menu.other_features.title=Other Features
codewhisperer.statusbar.sub_menu.security_scans.title=Code Reviews
codewhisperer.statusbar.tooltip=Amazon Q status
codewhisperer.toolwindow.entry.prefix=[{0}] ACCEPTED recommendation with the following code provided with reference under 
codewhisperer.toolwindow.entry.suffix={1, choice, 0#|1#. Added to {0}} at line {2}
codewhisperer.toolwindow.popup.text=Reference code under the {0} license from repository {1}
codewhisperer.toolwindow.settings=Amazon Q Settings
codewhisperer.toolwindow.settings.prefix=Don't want suggestions that include code with references? Uncheck this option in 
codewhisperer.toolwindow.settings.prefix_sso=Your organization controls whether suggestions include code with references. To update these settings, please contact your admin
codewhisperer.trigger.document.unsupported=Amazon Q inline suggestion is not supported for this file
codewhisperer.trigger.error.client_side=Unable to show recommendations, please try again later
codewhisperer.trigger.error.server_side=Unable to get recommendations, please try again later
codewhisperer.trigger.ide.unsupported=Amazon Q inline suggestion is only supported in 2023.3+ IDE backends
codewhisperer.trigger.service=Invoke Amazon Q inline suggestions
common.none=None
configure.toolkit=Configure AWS Connection
configure.toolkit.upsert_credentials.action=Edit AWS Credential file(s)...
configure.toolkit.upsert_credentials.confirm_file_create=Credentials file {0} does not exist. Create it?
configure.toolkit.upsert_credentials.confirm_file_create.okay=Create
configure.toolkit.upsert_credentials.confirm_file_create.title=Create Credential File
configure.validate.no_region_specified=Must specify a region.
connection.pinning.unlink=Unlink connection from {0}
credentials.could_not_open=Could not open credential file {0} for editing
credentials.file.notification=AWS Toolkit profiles will be reloaded on save
credentials.individual_identity.connected=(connected to tools)
credentials.individual_identity.expired=(expired or invalid)
credentials.individual_identity.reconnect=Reconnect
credentials.individual_identity.signout=Sign out
credentials.invalid.description=Failed to validate your AWS credentials
credentials.invalid.invalid_selection=Please select a valid credential profile
credentials.invalid.more_info=More info...
credentials.invalid.not_found=''{0}'' (not found)
credentials.invalid.title=Invalid AWS Connection
credentials.mfa.action=Enter MFA code
credentials.mfa.display=''{0}'' requires MFA to connect
credentials.mfa.display.short=MFA code required
credentials.mfa.message=Enter MFA code for device: {0}
credentials.mfa.title=MFA Required to Connect to AWS Using {0}
credentials.pending.title=Waiting for sign in
credentials.pending.user_cancel.message=Login canceled by user
credentials.profile.assume_role.duplicate_source=profile ''{0}'' contains both properties ''source_profile'' and ''credential_source''
credentials.profile.assume_role.invalid_credential_source=profile ''{0}'' is not using a valid ''credential_source''
credentials.profile.assume_role.missing_source=profile ''{0}'' missing either property ''source_profile'' or ''credential_source''
credentials.profile.circular_profiles=Profile ''{0}'' is invalid due to a circular profile dependency found between {1}
credentials.profile.credential_process.execution_exception_prefix=Failed to execute credential_process ({0})
credentials.profile.credential_process.parse_exception_prefix=Failed to parse credential_process response
credentials.profile.credential_process.timeout_exception_prefix=Execution of credential_process ({0}) timed out
credentials.profile.failed_load=Failed to load AWS profiles
credentials.profile.missing_property=Profile ''{0}'' is missing required property {1}
credentials.profile.name=Profile:{0}
credentials.profile.not_configured=No active credential provider configured
credentials.profile.refresh_errors=Failed to load {0, choice, 1#1 profile|2#{0,number} profiles}.
credentials.profile.refresh_ok_message={0, choice, 1#1 profile|2#{0,number} profiles} found.
credentials.profile.refresh_ok_title=Reloaded AWS Credential Profiles
credentials.profile.source_profile_not_found=Profile ''{0}'' references source profile ''{1}'' which does not exist
credentials.profile.unsupported=Profile ''{0}'' is not using role-based, session-based, process-based, or basic credentials.
credentials.profile.validation_error=Failed to switch to profile ''{0}''
credentials.refreshing=Refreshing tokens
credentials.retrieving=Retrieving AWS credentials
credentials.sono.login=Sign in with AWS Builder ID
credentials.sono.login.cancelled=AWS Builder ID login cancelled
credentials.sono.login.failed=AWS Builder ID login Failed
credentials.sono.login.message=Sign in with AWS Builder ID at {0}, code: {1}
credentials.sono.login.pending=Waiting for AWS Builder ID sign in
credentials.sono.login.refreshing=Refreshing tokens from AWS Builder ID
credentials.sono.login.title=AWS Builder ID Login Required
credentials.sso.action=Start AWS IAM Identity Center login
credentials.sso.display=''{0}'' requires you to re-login with AWS IAM Identity Center
credentials.sso.display.short=AWS IAM Identity Center login required
credentials.sso.login.cancelled=AWS IAM Identity Center login cancelled
credentials.sso.login.failed=AWS IAM Identity Center Login Failed
credentials.sso.login.message=Login to AWS at {0}, code: {1}
credentials.sso.login.open_browser=Open Browser
credentials.sso.login.pending=Waiting for AWS IAM Identity Center sign in
credentials.sso.login.refreshing=Refreshing tokens from AWS IAM Identity Center
credentials.sso.login.session=Sign in to SSO session ''{0}''
credentials.sso.login.title=AWS IAM Identity Center Login Required
credentials.ssoSession.validation_error=Profile ''{0}'' references sso-session ''{1}'' which does not exist
credentials.switch.confirmation.comment=Use {0} with {1} while using {2} with other services.
credentials.switch.confirmation.no=Not now
credentials.switch.confirmation.title=Stay connected to {0} with {1}?
credentials.switch.confirmation.yes=Yes
credentials.switch.notification.title={0} will now always use {1}
credentials.welcome.provide=Provide AWS Credentials
datagrip.auth.secrets_manager=SecretsManager Auth
datagrip.secret_host=Use the url and port from the secret
datagrip.secret_id=Secret Name/ARN:
datagrip.secretsmanager.action=Connect with Secrets Manager...
datagrip.secretsmanager.action.confirm_continue=<html><p>{0}</p><br/><p>Continue setting up the database?</p></html>
datagrip.secretsmanager.action.confirm_continue_title=The selected secret seems to be for a different database
datagrip.secretsmanager.action.title=Select a Secret
datagrip.secretsmanager.validating=Validating selected secret
datagrip.secretsmanager.validation.different_address=Secret {0} specifies a different database address ''{1}''
datagrip.secretsmanager.validation.different_engine=Secret {0} specifies a different database engine ''{1}''
datagrip.secretsmanager.validation.exception=Exception thrown while validating secret
datagrip.secretsmanager.validation.failed_to_get=Failed to retrieve secret {0}
datagrip.secretsmanager.validation.no_host=Secret {0} does not have a field named ''host''
datagrip.secretsmanager.validation.no_password=Secret {0} does not have a field named ''password''
datagrip.secretsmanager.validation.no_port=Secret {0} does not have a field named ''port''
datagrip.secretsmanager.validation.no_secret=No SecretsManager secret specified
datagrip.secretsmanager.validation.no_username=Secret {0} does not have a field named ''username''
datagrip.secretsmanager.validation.unkown_engine=Unknown database engine {0}
datagrip.validation.invalid_credential_specified=Invalid credential profile {0} selected!
datagrip.validation.invalid_region_specified=Invalid region {0} selected!
date.in.n.hours={0,choice, 0#0 hours|1#1 hour|2#{0,number} hours}
date.in.n.minutes={0,choice, 0#0 minutes|1#1 minute|2#{0,number} minutes}
delete_resource.confirmation_text=delete me
delete_resource.delete_failed=Failed to delete {0} ''{1}''
delete_resource.deleted=Deleted {0} ''{1}''
delete_resource.message=Are you sure you want to delete {0} ''{1}''?\n\nType "delete me" below to confirm.
delete_resource.title=Delete {0} {1}
developerTool.toolWindow.help=Need help?
developerTool.toolWindow.help.docs=Learn more about connecting to AWS
developerTool.toolWindow.help.github=Connect with us on GitHub
developerTool.toolWindow.title=Developer Tools
developerTool.toolWindow.welcome.connect=Connect to an account to get started:
developerTool.toolWindow.welcome.creds.about=You can connect to and switch between\naccounts at any time in the toolkit.
docker.not.found=Docker not found
dockerfile.building=Building Dockerfile: {0}
dockerfile.label=Dockerfile:
dynamic_resources.add_remove_resources_tooltip={0} resources available
dynamic_resources.begin_resource_creation=Started new {0} creation...
dynamic_resources.create_resource_action=Create {0}
dynamic_resources.create_resource_file_empty=Resource model is empty. Do you want to continue?
dynamic_resources.create_resource_file_empty_title=Empty Resource Model
dynamic_resources.create_resource_file_name=Create new {0}
dynamic_resources.create_resource_instruction=Creating a resource: Enter resource properties, click Create
dynamic_resources.delete_resource=Delete resource...
dynamic_resources.edit_resource_instruction=File is read-only, click Edit to begin editing the resource
dynamic_resources.editor.enableEditingResource_text=Edit
dynamic_resources.editor.submitResourceUpdateRequest_text=Update
dynamic_resources.experiment.description=Enables basic JSON-based create, update, and delete support for cloud resources through the Resources node in the AWS Explorer 
dynamic_resources.experiment.title=JSON Resource Modification
dynamic_resources.fetch.fail.content=Failed to retrieve resource model for {0}
dynamic_resources.fetch.fail.title=Open resource model
dynamic_resources.fetch.fetch=Retrieving model
dynamic_resources.fetch.indicator_title=Open resource model for {0}
dynamic_resources.fetch.open=Opening model in IDE
dynamic_resources.loading_manifest=Loading AWS resource manifest
dynamic_resources.openFileForUpdate_text=Update resource...
dynamic_resources.openReadOnlyFile_text=View Resource
dynamic_resources.operation_status_failed=Failed to {1} {0}: {2}
dynamic_resources.operation_status_failed_no_message=Failed to {1} {0}
dynamic_resources.operation_status_notification_title={0} {1} status
dynamic_resources.operation_status_success=Successfully {1}d {0}
dynamic_resources.resource_creation={0} Creation
dynamic_resources.type.explorer.create_resource=Create resource...
dynamic_resources.type.explorer.view_documentation=View documentation
dynamic_resources.unavailable_in_region=Unavailable in {0}
dynamic_resources.update_resource_instruction=Click Update to save your changes
dynamic_resources.update_resource_no_changes_made=No changes made, do you want to continue editing?
dynamic_resources.update_resource_no_changes_made_title=Resource model unchanged
dynamodb.experiment.description=List, open and query Amazon DynamoDB tables
dynamodb.experiment.title=DynamoDB table viewer
dynamodb.viewer.open.failed=Failed to open DynamoDB table
dynamodb.viewer.open.failed.with_error=Failed to open DynamoDB table: {0}
dynamodb.viewer.search.index.global=Global
dynamodb.viewer.search.index.label=Table / Index:
dynamodb.viewer.search.index.local=Local
dynamodb.viewer.search.run.title=Run
dynamodb.viewer.search.title=Scan
ecr.copy_image_uri.action=Copy Tag URI
ecr.copy_uri.action=Copy Repository URI
ecr.create.app_runner_service.action=Create App Runner Service...
ecr.create.repo.action=Create Repository...
ecr.create.repo.title=Create ECR Repository
ecr.create.repo.validation.empty=Enter a name
ecr.delete.repo.action=Delete Repository...
ecr.delete.tag.action=Delete {0, choice, 1#Tag|2#{0,number} Tags}...
ecr.delete.tag.deleting=Deleting Tags
ecr.delete.tag.description=Are you sure you want to delete {0, choice, 1#1 tag|2#{0,number} tags} from repository {1}?
ecr.delete.tag.failed=Failed to delete {0, choice, 1#tag|2#{0,number} tags} from repository {1}
ecr.delete.tag.succeeded=Deleted {0, choice, 1#tag|2#{0,number} tags} from repository {1}
ecr.dockerfile.configuration.add=New configuration from Dockerfile...
ecr.dockerfile.configuration.edit=Edit Dockerfile configuration...
ecr.dockerfile.configuration.invalid=Invalid Dockerfile run configuration selected
ecr.dockerfile.configuration.invalid_server=Dockerfile run configuration has an invalid server connection
ecr.dockerfile.configuration.label=Dockerfile configuration:
ecr.dockerfile.label=Dockerfile:
ecr.image.not_selected=Image must be selected
ecr.pull.confirm=Pull
ecr.pull.progress=Pulling image from ECR: {0}:{1}
ecr.pull.title=Pull from ECR
ecr.push.confirm=Push
ecr.push.credential_fetch_failed=Failed to obtain authorization token from ECR
ecr.push.failed=Push failed: {0}
ecr.push.in_progress=Deployment with name '{0}' is already being pushed.
ecr.push.remoteTag=Remote Tag:
ecr.push.source=Local Image:
ecr.push.title=Push to ECR
ecr.push.type.dockerfile.label=Dockerfile
ecr.push.type.local_image.label=Local Image
ecr.push.unknown_exception=Failed to push to ECR
ecr.repo.label=ECR Repository:
ecr.repo.not_selected=ECR Repository must be selected
ecr.tag.invalid=Tag provided is not valid
ecs.clusters=Clusters
ecs.container_actions_group.label=Containers
ecs.execute_command.experiment.description=Enable executing commands and opening interactive terminal against a running ECS task/service
ecs.execute_command.experiment.title=ECS Execute Command
ecs.execute_command.label=Command:
ecs.execute_command.production_warning.checkbox_label=I confirm {0} is not a production service and wish to continue
ecs.execute_command_call_service=Executing command
ecs.execute_command_disable=Disable Command Execution
ecs.execute_command_disable_failed=Execute Command could not be disabled for {0}
ecs.execute_command_disable_in_progress=Cannot execute action since Execute Command is being disabled for {0}
ecs.execute_command_disable_progress_indicator_message=Disabling Command Execution for {0}
ecs.execute_command_disable_success=Execute Command for {0} has been disabled.
ecs.execute_command_disable_warning=<html>Disabling command execution will change the state of resources in your AWS account, including but not limited to stopping and restarting the service.<br> Do you wish to continue?</html>
ecs.execute_command_disable_warning_title=Disable Execute Command Warning
ecs.execute_command_enable=Enable Command Execution
ecs.execute_command_enable_failed=Execute Command could not be enabled for {0}
ecs.execute_command_enable_in_progress=Cannot execute action since Execute Command is being enabled for {0}
ecs.execute_command_enable_progress_indicator_message=Enabling Command Execution for {0}
ecs.execute_command_enable_success=Execute Command for {0} has been enabled.
ecs.execute_command_enable_warning=<html>Enabling command execution will change the state of resources in your AWS account, including but not limited to stopping and restarting the service.<br>Altering the state of resources while the Execute Command is enabled can lead to unpredictable results.<br> Do you wish to continue?</html>
ecs.execute_command_enable_warning_title=Enable Execute Command Warning
ecs.execute_command_failed=ECS Execute Command Failed
ecs.execute_command_no_command=Command to execute must be entered
ecs.execute_command_no_task_role_found_exception=No roles found
ecs.execute_command_permissions_not_verified=Permissions for ECS Exec could not be verified. Please ensure you have the right role and permissions before proceeding
ecs.execute_command_permissions_required_title=Permissions required for ECS Exec
ecs.execute_command_run=Run Command...
ecs.execute_command_run_command_default_text=Enter command...
ecs.execute_command_run_command_in_shell=Open Interactive Shell...
ecs.execute_command_shell.label=Shell:
ecs.execute_command_shell_comboBox_empty=Shell must be selected
ecs.execute_command_task.label=Task:
ecs.execute_command_task_comboBox_empty=Task must be selected
ecs.execute_command_task_role_invalid_warning=<html>You may not have the <a href= "https://docs.aws.amazon.com/AmazonECS/latest/developerguide/ecs-exec.html#ecs-exec-enabling-and-using">Permissions Required for using the execute command</a><br/>Please set up a Task Role with the required permissions and retry...</html>
ecs.execute_command_task_role_invalid_warning_title=Set up Valid Task Role with Permissions
ecs.no_services_in_cluster=Cluster has no services
ecs.run_config.container.loading.error=Error loading containers: {0}
ecs.service.container_logs.action_label=View Logs
ecs.service.logs.cannot_determine_log_stream=Cannot determine log streams for service {0}. Note: only AWSLOGS LogDriver is supported.
ecs.service.logs.cannot_find_container_log_stream=Cannot determine log stream for container {0}.
ecs.service.logs.empty=No events found in log stream: {0}
ecs.service.logs.no_log_stream=Running task has not sent events to CloudWatch, opening Log Group instead.
ecs.service.logs.no_running_tasks=Service has no running tasks, opening Log Group instead.
ecs.service.logs.title=ECS Service Logs
ecs.service.not_found=Service {0} not found in cluster {1}
ecs.task_definition.json_schema_name=AWS ECS Task Definition
ecs.task_definitions=Task Definitions
environment.variables.dialog.title=Environment Variables
executableCommon.auto_managed=Managed by AWS
executableCommon.auto_resolved=Auto-detected: {0}
executableCommon.cli_not_configured={0} executable not configured
executableCommon.configurable.title=External Tools
executableCommon.downloading=Downloading {0}...
executableCommon.empty_info={0} did not provide any output
executableCommon.failed_install=Failed to install {0}
executableCommon.installing=Installing {0}
executableCommon.latest_not_compatible=Latest version of {0} is not compatible with the toolkit''s supported range: {1}
executableCommon.missing_executable=Validation of {0} failed: {1}
executableCommon.not_installed=Not installed.
executableCommon.unexpected_output={0} provided unexpected output. Ensure that your {0} is up-to-date: "{1}"
executableCommon.updating=Updating {0}
executableCommon.validating=Validating {0}
executableCommon.version_parse_error=Could not parse {0} executable version from "{1}"
executableCommon.version_range_wrong=Bad {0} executable version. Expected version must be within the following ranges: {1} , but was {2}
executableCommon.version_too_high=Upgrade your AWS Toolkit plugin to resolve this issue.
executableCommon.version_too_low=Upgrade your {0} to resolve this issue.
executableCommon.version_too_low2=Upgrade {0} to resolve this issue. Minimum version for this feature is {1}
executableCommon.version_wrong=Bad {0} executable version. Expected {1} â¤ version < {2} but was {3}.
explorer.copy_arn=Copy Arn
explorer.copy_identifier=Copy identifier
explorer.create_new_issue=Create a New Issue on GitHub
explorer.empty_node=empty
explorer.error_loading_resources=Error Loading Resources ({0})
explorer.error_loading_resources_default_details=check log for details
explorer.error_loading_resources_not_connected=check credentials/region
explorer.label=AWS Explorer
explorer.node.apprunner=App Runner
explorer.node.cloudformation=CloudFormation
explorer.node.cloudwatch=CloudWatch Logs
explorer.node.codemodernizer=CodeModernizer
explorer.node.dynamo=DynamoDB
explorer.node.ecr=ECR
explorer.node.ecs=ECS
explorer.node.lambda=Lambda
explorer.node.other=Resources
explorer.node.other.add_remove=Add or remove resource types...
explorer.node.rds=RDS
explorer.node.redshift=Redshift
explorer.node.s3=S3
explorer.node.schemas=Schemas
explorer.node.sqs=SQS
explorer.registry.no.schema.resources=Registry has no Schemas
explorer.results_truncated=Results truncated, double click to load more
explorer.stack.no.serverless.resources=Stack has no Lambda Functions
explorer.toolwindow.title=Explorer
explorer.view_documentation=View Documentation
explorer.view_source=View Source on GitHub
feedback.comment.emptyText=optional
feedback.comment.label=Please enter your feedback
feedback.comment.textbox.initial.length=2000 characters remaining
feedback.comment.textbox.title=What do you like about {0}? What can we improve?
feedback.comment.textbox.title.amazonq=How was your experience with the upgrade of your Java application?
feedback.comment.textbox.title.amazonq.feature_dev=How has your experience with Amazon Q been? What can we improve?
feedback.comment.textbox.title.amazonq.test_generation=How has your experience with Amazon Q Test Generation been? What can we improve?
feedback.connect.with.github.title=Join us on GitHub
feedback.customer.alert.info=Please don't add personally identifiable information (PII), confidential or sensitive information in your feedback.<br>Remove any PII when sharing file paths, error messages, etc.
feedback.description=Submit quick feedback about the AWS Toolkit for JetBrains
feedback.github.link=<html>Have an issue or feature request?<br><a href="{0}">Talk to us on GitHub instead!</a></html>
feedback.initial.help.text=<br>Looking for help? View the <a href = "https://docs.aws.amazon.com/toolkit-for-jetbrains/latest/userguide/welcome.html#welcome-">Getting Started Guide</a> or search our <a href="https://docs.aws.amazon.com/toolkit-for-jetbrains/latest/userguide/welcome.html">documentation</a>
feedback.limit.label={0} characters remaining
feedback.report.issue.link=Report an issue
feedback.request.feature.link=Request a feature
feedback.share.feedback.title=Share Feedback
feedback.smiley.happyTooltip=Positive
feedback.smiley.question=How was your experience?
feedback.smiley.sadTooltip=Negative
feedback.submit_button=Share
feedback.submit_failed=An exception occurred while sharing your feedback: {0}
feedback.submit_failed_title=Failed to share feedback
feedback.submit_success=Thanks for the feedback!
feedback.submitting=Sharing...
feedback.title=Share Feedback for {0}...
feedback.title.amazonq=Send feedback for Code Transformation by Amazon Q
feedback.title.amazonq.send_feedback=Send feedback for Amazon Q
feedback.validation.comment_too_long=Comment is too long.
feedback.validation.empty_comment=Please provide a comment.
feedback.validation.no_sentiment=Please select how you're feeling.
feedback.view.source.code.link=View Source Code and Contribute
gateway.auth.different.account.already.signed.in=You are signed in with {0}. Do you want to continue?
gateway.auth.different.account.required=You are signed into a different account. \nDo you want to sign in with the account associated with {0}?
gateway.auth.different.account.sign.in=Sign in with a different account
gateway.connection.workflow.copy_scripts=Copy Scripts
gateway.connection.workflow.git_clone=Git Clone
gateway.connection.workflow.install_toolkit=Install AWS Toolkit
gateway.connection.workflow.prime_ssh_agent=Prime SSH Agent
gateway.connection.workflow.start_ide=Start IDE
gateway.connection.workflow.step_failed=\nStep failed exceptionally\n
gateway.connection.workflow.step_skipped=Step skipped
gateway.connection.workflow.step_successful=\nStep completed successfully\n
general.acknowledge=Acknowledge
general.add.another=Add another
general.auth.reauthenticate=Reauthenticate
general.cancel=Cancel
general.canceling=Canceling
general.close_button=Close
general.configure_button=Configure
general.confirm=Confirm
general.confirm_proceed=Please confirm before proceeding
general.create=Create
general.create_button=Create
general.create_in_progress=Creating...
general.default=Default
general.delete=Delete
general.delete_accessible_name=Delete confirmation box
general.details=(details)
general.dismiss=Dismiss
general.execute_button=Execute
general.execution.canceled=canceled
general.execution.cli_error=Command did not exit successfully, exit code: {0}\n
general.execution.failed=failed
general.execution.running=running...
general.execution.success=completed successfully
general.file_not_found=File not found: "{0}"
general.get_started=Get started
general.help=Help
general.in_progress_button=In progress
general.logs=Logs
general.message=Message
general.more=More
general.more_dialog=More...
general.name.label=Name:
general.no_changes=No changes were provided
general.notification.action.hide_forever=Don't show again
general.notification.action.hide_once=Dismiss
general.ok=OK
general.open.in.progress=Opening...
general.open_in_aws_console=Open in AWS Console
general.open_in_aws_console.error=Failed to open link in browser
general.open_in_aws_console.no_permission=Failed to open link in browser because user credentials are not allowed to perform sts:GetFederationToken
general.optional=Optional
general.policy=Policy
general.refresh=Refresh
general.reject=Reject
general.save=Save
general.select_button=Select
general.step.canceled={0} has been canceled
general.step.failed={0} has failed: {1}
general.success=Complete...
general.time=Time
general.time.five_minutes=Five Minutes
general.time.one_minute=One Minute
general.time.ten_minutes=Ten Minutes
general.unknown_error=Unknown error. See IDE logs for more details.
general.update_button=Update
general.update_in_progress=Updating...
gettingstarted.auth.builderid.expired=AWS Builder ID Expired
gettingstarted.auth.config.issue=There was an issue reading your AWS <a href="https://docs.aws.amazon.com/cli/latest/userguide/cli-configure-files.html">config file(s)</a>: {0}
gettingstarted.auth.connected.builderid=Connected with AWS Builder ID
gettingstarted.auth.connected.iam=Connected with IAM
gettingstarted.auth.connected.idc=Connected with IAM Identity Center
gettingstarted.auth.failed=Failed to Authenticate
gettingstarted.auth.iam.invalid=Invalid IAM Credentials
gettingstarted.auth.idc.expired=IAM Identity Center Expired
gettingstarted.auth.idc.sign.out.confirmation=Signing out will remove profiles associated with this connection from the ~/.aws/config file
gettingstarted.auth.idc.sign.out.confirmation.title=Confirm deletion of profile
gettingstarted.builderid.description=Personal profile for builders
gettingstarted.codecatalyst.open.explorer=Open CodeCatalyst menu
gettingstarted.codecatalyst.panel.create.space=Create a CodeCatalyst space
gettingstarted.codecatalyst.panel.setup=Set up CodeCatalyst
gettingstarted.codewhisperer.remote=Amazon Q chat are unavailable in JetBrains Gateway
gettingstarted.connecting.in.browser=Connecting in browser...
gettingstarted.editor.title=Authenticate with AWS Toolkit
gettingstarted.explorer.gotit.codecatalyst.body=Launch Dev Environments and find more features here.
gettingstarted.explorer.gotit.codecatalyst.title=Get started with CodeCatalyst
gettingstarted.explorer.gotit.explorer.body=IAM Credentials you add will be accessible from this dropdown. Add and edit credentials from the "..." menu.
gettingstarted.explorer.gotit.explorer.title=Switch and edit credentials
gettingstarted.explorer.iam.add=Add IAM Credentials
gettingstarted.explorer.iam.add.info=Provide IAM Credentials to work with AWS Services and Resources from the Explorer.
gettingstarted.explorer.iam.switch=Switch to an IAM role in the dropdown above to work with AWS Services and Resources in the Explorer
gettingstarted.explorer.new.setup=Setup Authentication
gettingstarted.explorer.new.setup.info=To get started with the Explorer, setup authentication to AWS
gettingstarted.explorer.open.menu=Open Resource Explorer
gettingstarted.iam.description=Long-term programmatic access
gettingstarted.panel.learn_more=Learn more
gettingstarted.panel.login_button=Use for free, no AWS account required
gettingstarted.setup.auth.failure.body=We are unable to connect or the process was cancelled. Please try again, <a href='https://docs.aws.amazon.com/toolkit-for-jetbrains/latest/userguide/auth-access.html'>visit documentation</a>, or <a href='https://github.com/aws/aws-toolkit-jetbrains/issues/new'>file an issue on GitHub</a> to troubleshoot
gettingstarted.setup.auth.failure.title=Authentication Failed
gettingstarted.setup.auth.no_iam=Amazon Q do not support authentication with IAM Credentials
gettingstarted.setup.auth.success.body=You can start using {0} or setup another authentication method below.
gettingstarted.setup.auth.success.iam.body=We've connected your local credentials. Get started with the Resource Explorer or setup another feature below.
gettingstarted.setup.auth.success.iam.title=Connected to IAM Credentials
gettingstarted.setup.auth.success.title=Connected to {0}
gettingstarted.setup.builderid.bullets=Sign up for free\nComplement your existing AWS accounts\nSecure login with optional MFA
gettingstarted.setup.builderid.notice=AWS Builder ID is a new personal profile for builders. <a href="https://docs.aws.amazon.com/signin/latest/userguide/sign-in-aws_builder_id.html">Learn more</a>
gettingstarted.setup.codecatalyst.no_iam=CodeCatalyst does not support authentication with IAM Credentials
gettingstarted.setup.codewhisperer.use_builder_id=If you haven't been provided an Amazon Q license, use AWS Builder ID
gettingstarted.setup.codewhisperer.use_identity_center=If you have an Amazon Q license, please use IAM Identity Center
gettingstarted.setup.connect=Connect
gettingstarted.setup.error.not_empty=Must not be empty
gettingstarted.setup.error.not_selected=Selection must be made
gettingstarted.setup.explorer.no_builder_id=Resource Explorer does not support authentication with AWS Builder ID.
gettingstarted.setup.iam.access_key=Access Key ID:
gettingstarted.setup.iam.access_key.invalid=Access key must be alphanumeric and between 16 and 128 characters
gettingstarted.setup.iam.notice=Credentials will be automatically added to your local ~/.aws/config file as a profile.<br/><a>Edit credential file directly...</a>
gettingstarted.setup.iam.profile=Profile Name:
gettingstarted.setup.iam.profile.comment=Used by AWS Toolkit to list added credentials
gettingstarted.setup.iam.profile.exists=Profile with name ''{0}'' already exists
gettingstarted.setup.iam.profile.invalid_credentials=The provided credentials are not valid
gettingstarted.setup.iam.secret_key=Secret Access Key:
gettingstarted.setup.iam.session.exists=SSO Session with name ''{0}'' already exists
gettingstarted.setup.idc.no_builder_id=User should not perform Identity Center login with AWS Builder ID url
gettingstarted.setup.idc.profile.comment=User-specified name used to label credentials locally
gettingstarted.setup.idc.region=Region:
gettingstarted.setup.idc.role.title=AWS Toolkit: Add IAM Identity Center Roles
gettingstarted.setup.idc.roleLabel=Select roles from IAM Identity Center to use with the AWS Toolkit
gettingstarted.setup.idc.startUrl=Start URL:
gettingstarted.setup.idc.startUrl.comment=URL for your organization, provided by an administrator or help desk
gettingstarted.setup.learnmore=Learn More
gettingstarted.setup.tabs.builderid=AWS Builder ID
gettingstarted.setup.tabs.iam=IAM Credentials
gettingstarted.setup.tabs.idc=IAM Identity Center
gettingstarted.setup.title=AWS Toolkit: Setup Authentication
group.aws.toolkit.dynamoViewer.changeMaxResults.text=Max Results
group.aws.toolkit.dynamoViewer.toolbar.settings.text=Settings
group.aws.toolkit.jetbrains.core.services.cwc.actions.ContextMenuActions.text=Amazon Q
group.aws.toolkit.s3viewer.contextMenu.copyGroup.text=Copy
iam.create.role.in_progress=Creating...
iam.create.role.managed_policies=Managed Policies:
iam.create.role.missing.role.name=Role name is required.
iam.create.role.name.label=Name:
iam.create.role.policy.editor.name=Permissions
iam.create.role.title=Create IAM Role
iam.create.role.trust.editor.name=Trust Relationships:
iam.name=IAM
iam_identity_center.name=IAM Identity Center
iam_identity_center.service_name=IAM Identity Center ({0})
iam_identity_center.sign_out=Sign out of IAM Identity Center
lambda.build.java.unsupported_build_system=Module ''{0}'' is not managed by Maven or Gradle
lambda.build.module_with_no_content_root=Module ''{0}'' lacks a content root
lambda.build.typescript.compiler.annotation_results=Annotation Results:
lambda.build.typescript.compiler.creating_config=Creating TypeScript config from scratch
lambda.build.typescript.compiler.emitted_files=Emitted Files:
lambda.build.typescript.compiler.ide_error=TypeScript compiler failed to start
lambda.build.typescript.compiler.processed_files=Processed Files:
lambda.build.typescript.compiler.running=Running TypeScript compiler against temporary config file ''{0}''
lambda.build.typescript.compiler.step=Compile TypeScript
lambda.build.typescript.compiler.using_base=Using TypeScript config ''{0}'' as a base
lambda.build.typescript.compiler.using_base_error=Unable to read existing TypeScript config ''{0}''
lambda.build.unable_to_locate_handler_root=Unable to locate root directory of the handler
lambda.build.unable_to_locate_project_root=Unable to locate project directory for Module ''{0}''
lambda.create.description.label=Description:
lambda.create.enable.xray.label=Enable AWS X-Ray
lambda.create.env_vars.label=Environment Variables:
lambda.create.function_name.label=Name:
lambda.create.iam.role.label=IAM Role:
lambda.create.memory.label=Memory (MB):
lambda.create.source_bucket.label=Source Bucket:
lambda.create.step.build=SAM Build
lambda.create.step.create_lambda=Creating new Lambda
lambda.create.step.package=SAM Package
lambda.create.step.update_lambda=Updating Lambda
lambda.create.timeout.label=Timeout (seconds):
lambda.create_new=Create new AWS Lambda...
lambda.credentials.tooltip=The existing AWS account connection to use for this configuration.
lambda.debug.attach.error=Debugger attach error
lambda.debug.attach.fail=Unable to attach to debugger process.
lambda.debug.docker.not_connected=Running/Debugging Lambdas locally requires Docker. Please make sure it is installed and running
lambda.debug.process.start.timeout=Timeout waiting for Debugger Worker process to start
lambda.debug.step.start_sam=Running SAM local invoke
lambda.debug.waiting=Waiting for debugger...
lambda.execute.function_error=Function error: {0}
lambda.execute.invoke=Invoking Lambda function: {0}
lambda.execute.logs=Logs: \n{0}
lambda.execute.output=Output: \n{0}
lambda.execute.service_error=Error invoking Lambda: {0}
lambda.function.architecture.label=Architecture:
lambda.function.architecture.tooltip=The AWS Lambda architecture to use when running this function.
lambda.function.code_updated.notification=Code updated for function ''{0}''.
lambda.function.configuration_updated.notification=Configuration updated for function ''{0}''.
lambda.function.created.notification=Function ''{0}'' created.
lambda.function.delete.action=Delete Function...
lambda.function.enableXRay.tooltip=Enables AWS X-Ray to detect, analyze, and optimize performance issues when this function runs.
lambda.function.env_vars.tooltip=The environment variables available to this function, specified as key/value pairs.
lambda.function.handler.tooltip=The name of the method within your code that AWS Lambda calls to execute your function. It must be a fully qualified name depending on the runtime.
lambda.function.iamRole.tooltip=The function's execution role.
lambda.function.label=Function:
lambda.function.memory.tooltip=The memory available to AWS Lambda when running this function, if needed.
lambda.function.name.tooltip=The function's name. It can only contain alphanumeric (A-Z, a-z, 0-9), underscore (_) and hyphen (-) characters and must not exceed 64 characters in length.
lambda.function.runtime.tooltip=The AWS Lambda runtime to use when running this function.
lambda.function.sourceBucket.tooltip=The Amazon S3 bucket in the same AWS Region to deploy this AWS Lambda function.
lambda.function.timeout.tooltip=The number of seconds until AWS Lambda stops running this function, if necessary.
lambda.function.updateCode.action=Update Function Code
lambda.function.updateConfiguration.action=Update Function Configuration
lambda.handler.label=Handler:
lambda.image.missing_debugger=Runtime ''{0}'' is unsupported for debugging image based Lambdas
lambda.image.sam_version_too_low=Sam CLI version {0} is too old to run image-based configurations, {1} or higher is required
lambda.input.label=Input
lambda.logs.does_not_exist=Lambda ''{0}'' has not sent events to CloudWatch
lambda.region.tooltip=The AWS Region to use for the connected AWS account.
lambda.run.configuration.handler_root_not_found=Failed to locate the root of the handler
lambda.run_configuration.credential_error=Error retrieving credentials: {0}.
lambda.run_configuration.credential_not_found_error=The credential provider with ID ''{0}'' could not be found.
lambda.run_configuration.debug_host=Debug Host:
lambda.run_configuration.description=Invoke AWS Lambda Function
lambda.run_configuration.fromTemplate.tooltip=The location and file name of the AWS SAM template to use for this configuration, and the resource in that template to associate with this configuration.
lambda.run_configuration.handler.validation.in_progress=Lambda handler validation is in progress
lambda.run_configuration.handler_not_found=Cannot find handler ''{0}'' in project.
lambda.run_configuration.input.file.label=File:
lambda.run_configuration.input.file.tooltip=The location and file name of the data to pass into the function. The data must follow the syntax for AWS Lambda event data.
lambda.run_configuration.input.samples.confirm=Selecting this template will replace the input text. Continue?
lambda.run_configuration.input.samples.confirm.title=Confirm Template Selection
lambda.run_configuration.input.samples.label=-- Event Templates --
lambda.run_configuration.input.text.label=Text:
lambda.run_configuration.input.text.tooltip=The data to pass into the function. The data must follow the syntax for AWS Lambda event data.
lambda.run_configuration.input_file_error=Failed to load input file: {0}
lambda.run_configuration.local=Local
lambda.run_configuration.no_architecture_specified=Must specify an architecture.
lambda.run_configuration.no_credentials_specified=Select AWS credentials in 'AWS Connection'
lambda.run_configuration.no_function_specified=Must specify function name or ARN.
lambda.run_configuration.no_handler_specified=Must specify a handler.
lambda.run_configuration.no_input_specified=Must specify an input.
lambda.run_configuration.no_runtime_specified=Must specify a supported runtime.
lambda.run_configuration.remote=Remote
lambda.run_configuration.remote.function.tooltip=The name of the AWS Lambda function to use.
lambda.run_configuration.sam=SAM CLI
lambda.run_configuration.sam.additional_build_args=Build Args:
lambda.run_configuration.sam.additional_local_args=Local Invoke Args:
lambda.run_configuration.sam.docker_network=Docker Network:
lambda.run_configuration.sam.invalid_executable=Invalid SAM CLI executable configured: {0}
lambda.run_configuration.sam.no_function_specified=Function logical name must be specified.
lambda.run_configuration.sam.no_such_function=Function {0} doesn''t exist in template {1}.
lambda.run_configuration.sam.no_template_specified=Template must be specified.
lambda.run_configuration.sam.skip_image_pull=Skip checking for newer container images
lambda.run_configuration.sam.template_file_not_found=Template file not found.
lambda.run_configuration.sam.validating=Validating SAM executable...
lambda.run_configuration.sam.validation.in_progress=SAM executable validation is in progress
lambda.run_configuration.source.from_handler=From &handler
lambda.run_configuration.source.from_template=From &template
lambda.run_configuration.unsupported_architecture=The architecture ''{0}'' is unsupported.
lambda.run_configuration.unsupported_runtime=The runtime ''{0}'' is unsupported.
lambda.runtime.label=Runtime:
lambda.sam.additionalBuildArgs.tooltip=Additional args to add to the 'sam build' command
lambda.sam.additionalLocalArgs.tooltip=Additional args to add to the 'sam local invoke' command
lambda.sam.buildInContainer.tooltip=Builds the serverless application's functions inside an AWS Lambda-like Docker container locally before deployment.
lambda.sam.debugHost.tooltip=The host to connect the debugger to
lambda.sam.dockerNetwork.tooltip=The name or ID of an existing Docker network that the AWS Lambda Docker containers should connect to, along with the default bridge network.
lambda.sam.skipCheckingNewerContainerImage.tooltip=Skips pulling down the latest Docker image for the specified AWS Lambda runtime.
lambda.service_name=AWS Lambda
lambda.slider_validation=The specified value must be an integer and between {0} and {1}
lambda.upload.build_settings=Build Settings
lambda.upload.code_location_settings=Code Location Settings
lambda.upload.configuration_settings=Configuration Settings
lambda.upload.create.title=Create Function
lambda.upload.deployment_settings=Deployment Settings
lambda.upload.updateCode.title=Update Code for {0}
lambda.upload.updateConfiguration.title=Update Configuration for {0}
lambda.upload.update_settings_button.title=Update
lambda.upload_validation.dockerfile_not_found=Dockerfile not found
lambda.upload_validation.function_name=Function Name must be specified
lambda.upload_validation.function_name_invalid=Function names can only contain alphanumerics, hyphen (-) and underscore (_)
lambda.upload_validation.function_name_too_long=Function names must not exceed {0} characters in length
lambda.upload_validation.handler=Handler must be specified
lambda.upload_validation.handler_not_found=Must be able to locate the handler in the project in order to deploy to Lambda
lambda.upload_validation.iam_role=IAM role must be specified
lambda.upload_validation.iam_role.loading=Still loading IAM role...
lambda.upload_validation.module_not_found=Failed to locate module for {0}
lambda.upload_validation.repo=Repository must be specified in order to deploy to Lambda
lambda.upload_validation.runtime=Runtime must be specified
lambda.upload_validation.source_bucket=Bucket must be specified in order to deploy to Lambda
lambda.upload_validation.source_bucket.loading=Still loading S3 bucket...
lambda.upload_validation.unsupported_runtime=Deploying using the runtime {0} is not supported
lambda.workflow.create_new.name=Create new Lambda
lambda.workflow.create_new.wait_for_stable=Waiting for function to be created
lambda.workflow.update_code.name=Update Lambda code
lambda.workflow.update_code.wait_for_stable=Waiting for function to stabilize
lambda.workflow.update_code.wait_for_updatable=Waiting for function to transition to an updatable state
loading_resource.failed=Failed loading resources
loading_resource.loading=Loading...
loading_resource.still_loading=Resources are still loading
notification.changelog=Changelog
notification.expand=Expand
notification.learn_more=Learn more
notification.update=Update
plugin.incompatible.fix=Disable incompatible plugins and restart IDE
plugin.incompatible.message=The plugin versions for Amazon Q, AWS Toolkit, and AWS Toolkit Core must match or conflicts may occur.
plugin.incompatible.title=AWS Plugin Incompatibility
q.connection.disconnected=You don't have access to Amazon Q. Please authenticate to get started.
q.connection.expired=Your Amazon Q session has timed out. Re-authenticate to continue.
q.connection.invalid=You don't have access to Amazon Q. Please authenticate to get started.
q.connection.need_scopes=You haven't enabled Amazon Q in Jetbrains.
q.enable.text=Enable Amazon Q to begin
q.learn.more=Learn More
q.migration.notification.title=Menu moved to status bar
q.node.title=Amazon Q
q.onboarding.button.text=Ask a question
q.onboarding.codewhisperer.description=Amazon Q inline suggestions are also enabled.
q.onboarding.description=Amazon Q is your generative-AI powered assistant.
q.onboarding.title=Meet Amazon Q
q.reauthenticate=Re-authenticate to connect
q.session_configuration=Extend your IDE sessions
q.session_configuration.description=Your maximum session length for Amazon Q can be extended to 90 days by your administrator. For more information, refer to How to extend the session duration for Amazon Q in the IDE in the IAM Identity Center User Guide.
q.sign.in=Get Started
q.ui.prompt.transform=/transform
q.unavailable=\  Not supported in v2023.2.0
q.unavailable.node=Please update to the latest IDE version
q.window.title=Amazon Q Chat
rds.aurora=Aurora
rds.iam_config=Connect with IAM...
rds.iam_connection_display_name=AWS IAM
rds.iam_help=The "RDS Host" and "RDS Port" are used to sign the request to AWS. They must match the values in the service. To use a proxy, edit the "Host" and "Port" fields.
rds.mysql=MySQL
rds.port=RDS Port:
rds.postgres=PostgreSQL
rds.url=RDS Host:
rds.validation.aurora_mysql_ssl_required=Aurora MySQL requires SSL to be enabled to connect
rds.validation.iam_sso_connection.error_info=No token found, please login and try again
rds.validation.no_iam_auth=Database {0} does not have IAM authentication enabled
rds.validation.no_instance_host=No RDS database host specified
rds.validation.no_instance_port=No RDS database port specified
rds.validation.setup_guide=See the RDS guide for IAM authentication
rds.validation.username=A non-empty username is required
redshift.auth.aws=IAM Auth
redshift.cluster_id=Cluster ID:
redshift.connect_aws_credentials=Connect with IAM...
redshift.validation.cluster_does_not_exist=Cluster {0} does not exist in region {1}!
redshift.validation.invalid_credential_specified=Invalid credential profile {0} selected!
redshift.validation.invalid_region_specified=Invalid region {0} selected!
redshift.validation.no_cluster_id=No cluster ID specified!
redshift.validation.username=A non-empty username is required
resource.delete.warning_text=Deletion of the {0} may take more than a minute to be reflected
run_configuration_extension.currently_selected=Use the currently selected credential profile/region
run_configuration_extension.feature.go.description=Allow injecting AWS Credentials via Environment Variables into GoLang based Run Configurations
run_configuration_extension.feature.go.title=AWS connected GoLang Run Configurations
run_configuration_extension.feature.java.description=Allow injecting AWS Credentials via Environment Variables into some Java-based Run Configurations
run_configuration_extension.feature.java.title=AWS connected Java Run Configurations
run_configuration_extension.feature.python.description=Allow injecting AWS Credentials via Environment Variables into some Python-based Run Configurations
run_configuration_extension.feature.python.title=AWS connected Python Run Configurations
run_configuration_extension.inject_aws_connection_exception=Exception occurred attempting to inject region/credentials
run_configuration_extension.manual=Other credential profile/region
s3.bucket.label=Bucket Name/URI
s3.bucket.load.fail.title=Access denied to bucket
s3.bucket.name.label=Bucket Name:
s3.copy.bucket.action=Copy Name
s3.copy.path=Copy Path
s3.copy.uri=Copy S3 URI
s3.copy.url=Copy URL
s3.copy.url.failed=Failed to copy URL
s3.create.bucket.create=Create
s3.create.bucket.missing.bucket.name=Bucket name is required.
s3.create.bucket.title=Create S3 Bucket
s3.delete.bucket.action=Delete S3 Bucket
s3.delete.object.action=Delete...
s3.delete.object.cancel=Cancel
s3.delete.object.description=Are you sure you want to delete {0, choice, 1#this file|2#{0,number} files}?
s3.delete.object.failed=Delete object failed
s3.download.object.action=Download...
s3.download.object.browse.description.multiple=Select folder to save files to
s3.download.object.browse.description.single=Select file or folder to save file to
s3.download.object.browse.title=Select Download Location...
s3.download.object.conflict.description=File ''{0}'' already exists in directory ''{1}''
s3.download.object.conflict.overwrite=Overwrite
s3.download.object.conflict.overwrite_rest=Overwrite All Conflicts
s3.download.object.conflict.skip=Skip
s3.download.object.conflict.skip_rest=Skip All Conflicts
s3.download.object.failed=Failed to download object {0}
s3.download.object.progress=Downloading ''{0}''
s3.error_loading=Failed to load children
s3.last_modified=Last Modified
s3.load_more=load more...
s3.load_more_failed=Failed to load more! load more...
s3.name=Name
s3.new.folder=New Folder...
s3.new.folder.name=Folder name:
s3.object.load.fail.title=Unable to load objects - Access denied
s3.open.file_too_big=The editor cannot open files larger than {0}
s3.open.viewer.bucket.failed=Failed to open bucket
s3.open.viewer.bucket_does_not_exist=Bucket {0} does not exist.
s3.open.viewer.failed=Failed to open file in editor.
s3.open.viewer.failed.unsupported=Failed to open file in editor. Unsupported file type.
s3.open.viewer.prefix.message=Specify S3 Key prefix:
s3.open.viewer.prefix.title=Enter Key Prefix
s3.prefix.filter=Filter by prefix
s3.prefix.label=Prefix: {0}
s3.rename.object.action=Rename...
s3.rename.object.failed=Failed to rename object
s3.rename.object.title=Rename: ''{0}'' to
s3.size=Size
s3.upload.directory.impossible=Cannot upload ''{0}'' because it is a directory
s3.upload.object.action=Upload...
s3.upload.object.failed=Failed to upload object {0}
s3.upload.object.progress=Uploading ''{0}''
s3.version.history.view=Show versions
sam.build.failed=SAM build command failed
sam.build.running=Running SAM build
sam.build.succeeded=SAM build command succeeded
sam.build.title=SAM build
sam.cli.version.upgrade.instructions=Learn more about upgrading SAM CLI
sam.cli.version.upgrade.reason=Learn more about SAM sync improvements...
sam.cli.version.upgrade.required=Your SAM CLI version is {0} and does not include performance improvements for Sync Serverless Applications. To continue, please upgrade to SAM CLI version {1} or higher.
sam.cli.version.upgrade.required.windows=If you are on SAM CLI version 1.85, 1.86 or 1.86.1, it has <a href= 'https://github.com/aws/aws-sam-cli/issues/5243'>known issues</a>. Please consider <a href= 'https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/install-sam-cli.html'>re-installing</a> / <a href= 'https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/manage-sam-cli-versions.html#manage-sam-cli-versions-upgrade'>upgrading</a> the latest version.
sam.cli.version.warning=SAM CLI version
sam.debug.attach=Attach Debugger
sam.debug.attach.parent=Attempt Debugger Attachment
sam.debug.dotnet_find_pid=Find DotNet process PID
sam.debug.find_container=Find Container
sam.executable.minimum_too_low_architecture=The architecture {0} requires a minimum SAM CLI version of {1}
sam.executable.minimum_too_low_runtime=The runtime {0} requires a minimum SAM CLI version of {1}
sam.init.description=AWS Serverless Application Model (AWS SAM) prescribes rules for expressing Serverless applications on AWS.
sam.init.error.no.architecture.selected=No architecture selected
sam.init.error.no.project.basepath=Unable to determine project basepath
sam.init.error.no.runtime.selected=No runtime selected
sam.init.error.no.solution.basepath=Unable to determine solution basepath
sam.init.error.no.template.selected=No template selected
sam.init.error.no.virtual.file=Unable to resolve location
sam.init.error.solution.create.fail=Unable to create solution
sam.init.error.subfolder_not_one=The SAM init root folder ''{0}'' should have only one sub folder.
sam.init.execution_error=Could not execute `sam init`!
sam.init.generating.schema=Generating Schema
sam.init.generating.template=Generating SAM template
sam.init.go.sdk=Go SDK:
sam.init.group.name=Other
sam.init.name=AWS Serverless Application
sam.init.node_interpreter.label=Node interpreter:
sam.init.packaging=Package Type:
sam.init.packaging.image=Image
sam.init.packaging.image.description=Artifact is an image uploaded to a ECR image repository
sam.init.packaging.zip=Zip
sam.init.packaging.zip.description=Artifact is a zip file uploaded to S3
sam.init.sam_template.tooltip=The name of the AWS Serverless Application Model (AWS SAM) template to use.
sam.init.schema.aws_credentials_select=Select AWS Credentials
sam.init.schema.aws_credentials_select_region=Select AWS Region
sam.init.schema.label=Event Schema:
sam.init.schema.pleaseSelect=Choose the EventBridge serverless event schema
sam.init.schema.registry.name=Registry: {0}
sam.init.sdk.error=Check SDK settings
sam.init.sdk.label=SDK:
sam.init.sdk.runtime.not.selected=No runtime selected
sam.init.select_sam_template=SAM Template:
sam.init.template.dynamodb_cookiecutter.description=Sample SAM Template to interact with DynamoDB Events
sam.init.template.dynamodb_cookiecutter.name=AWS SAM DynamoDB Event Example
sam.init.template.event_bridge_hello_world.description=A Hello World app for Amazon EventBridge that invokes a Lambda for every EC2 instance state change in your account
sam.init.template.event_bridge_hello_world.name=AWS SAM EventBridge Hello World
sam.init.template.event_bridge_hello_world_gradle.name=AWS SAM EventBridge Hello World (Gradle)
sam.init.template.event_bridge_hello_world_maven.name=AWS SAM EventBridge Hello World (Maven)
sam.init.template.event_bridge_starter_app.description=A Starter app for Amazon EventBridge that invokes a Lambda based on a dynamic event trigger for an EventBridge Schema of your choice
sam.init.template.event_bridge_starter_app.name=AWS SAM EventBridge App from Scratch (for an Event schema)
sam.init.template.event_bridge_starter_app_gradle.name=AWS SAM EventBridge App from Scratch (for an Event schema) (Gradle)
sam.init.template.event_bridge_starter_app_maven.name=AWS SAM EventBridge App from Scratch (for an Event schema) (Maven)
sam.init.template.hello_world.description=A basic SAM app
sam.init.template.hello_world.name=AWS SAM Hello World
sam.init.template.hello_world_gradle.name=AWS SAM Hello World (Gradle)
sam.init.template.hello_world_maven.name=AWS SAM Hello World (Maven)
sam.init.template.hello_world_typescript.description=A sample app using TypeScript
sam.init.template.hello_world_typescript.name=AWS SAM TypeScript Hello World
schemas.schema.could_not_open=Could not fetch and display schema {0} contents
schemas.schema.download_code_bindings.action=Download Code Bindings
schemas.schema.download_code_bindings.download=Download
schemas.schema.download_code_bindings.failed_to_download=Unable to download schema code
schemas.schema.download_code_bindings.failed_to_extract=Unable to place schema code in workspace
schemas.schema.download_code_bindings.failed_to_extract_collision=Unable to place schema code in workspace because there is already a file {0} in the folder hierarchy
schemas.schema.download_code_bindings.failed_to_generate=Unable to generate schema code
schemas.schema.download_code_bindings.failed_to_poll=Unable to poll for generated schema code
schemas.schema.download_code_bindings.heading=Download code bindings for schema {0} in registry {1}
schemas.schema.download_code_bindings.language.label=Language:
schemas.schema.download_code_bindings.language.tooltip=The programming language of the Schema to download generated code bindings for
schemas.schema.download_code_bindings.latest=<latest>
schemas.schema.download_code_bindings.location.label=File Location:
schemas.schema.download_code_bindings.location.tooltip=Location for the generated Event schema code binding files to be placed into
schemas.schema.download_code_bindings.notification.downloading={0}: Downloading code...
schemas.schema.download_code_bindings.notification.extracting={0}: Extracting/copying code...
schemas.schema.download_code_bindings.notification.finished=Downloaded code for schema {0}!
schemas.schema.download_code_bindings.notification.generating={0}: Generating code (this may take a few seconds the first time)...
schemas.schema.download_code_bindings.notification.start=Downloading code for schema {0}...
schemas.schema.download_code_bindings.title=Download Code Bindings
schemas.schema.download_code_bindings.validation.fileLocation_invalid=Invalid code folder location
schemas.schema.download_code_bindings.validation.fileLocation_required=Schema code folder location must be specified
schemas.schema.download_code_bindings.validation.language_required=Schema code language must be specified
schemas.schema.download_code_bindings.validation.version_required=Schema version must be specified
schemas.schema.download_code_bindings.version.label=Version:
schemas.schema.download_code_bindings.version.tooltip=The version of the Schema to download generated code bindings for
schemas.schema.language.go1=Go 1+
schemas.schema.language.java8=Java 8+
schemas.schema.language.python3_6=Python 3.6+
schemas.schema.language.typescript=Typescript 3+
schemas.schema.view.action=View Schema
schemas.search.download.label=Download code for selected schema
schemas.search.error=Unable to search registry {0}
schemas.search.error.registry={0} ({1})
schemas.search.header.text.allRegistries=Search across all registries
schemas.search.header.text.singleRegistry=Search "{0}" registry
schemas.search.no_results=No schemas found
schemas.search.searching=Searching for schemas...
schemas.search.title=EventBridge Schemas Search
schemas.search.version.prefix=Search matched version: {0}
schemas.service_name=Amazon EventBridge Schemas
serverless.application.deploy=Deploy Serverless Application
serverless.application.deploy.abort=Process aborted
serverless.application.deploy.action.description=Deploy Serverless Application
serverless.application.deploy.action.name=Deploy
serverless.application.deploy.button.bucket.create=Create
serverless.application.deploy.change_set=Change set ARN:
serverless.application.deploy.change_set.title=Deploy The Change Set?
serverless.application.deploy.change_set_not_found=Failed to locate change set ARN
serverless.application.deploy.error.bad_parse=Error parsing SAM template {0}\n\n {1}
serverless.application.deploy.error.no_resources=Cannot find any resources in SAM template {0}
serverless.application.deploy.error.unsupported_runtime_group=The runtime {0} is not supported for deploying the SAM template {1}
serverless.application.deploy.execute_change_set=Continue Deployment
serverless.application.deploy.execution_failed=SAM did not complete successfully
serverless.application.deploy.label.bucket=S3 Bucket:
serverless.application.deploy.label.repo=ECR Repository:
serverless.application.deploy.label.stack.new=Create Stack:
serverless.application.deploy.label.stack.select=Update Stack:
serverless.application.deploy.review_required=Require confirmation before deploying
serverless.application.deploy.step_name.build=Build
serverless.application.deploy.step_name.create_change_set=Create Change Set
serverless.application.deploy.step_name.package=Package
serverless.application.deploy.template.parameters=Template Parameters
serverless.application.deploy.title=Deploy Serverless Application
serverless.application.deploy.toast.template_file_failure=Could not detect template file
serverless.application.deploy.tooltip.createStack=The name of the AWS CloudFormation stack that is created when deploying this serverless application.
serverless.application.deploy.tooltip.deploymentConfirmation=Instructs AWS CloudFormation to wait for you to finish creating or updating the corresponding stack by executing the stack's current change set in AWS CloudFormation.
serverless.application.deploy.tooltip.ecrRepo=An Amazon Elastic Container Registry Repository in the same AWS Region to deploy the Lambda images to.
serverless.application.deploy.tooltip.s3Bucket=An Amazon S3 bucket in the same AWS Region to deploy this serverless application.
serverless.application.deploy.tooltip.template.parameters=The parameters that the AWS Toolkit detected in the project's AWS SAM template file for this serverless application.
serverless.application.deploy.tooltip.updateStack=The name of the existing AWS CloudFormation stack that is used when deploying this serverless application.
serverless.application.deploy.use_container=Build function inside a container
serverless.application.deploy.validation.ecr.repo.empty=Select an ECR Repository
serverless.application.deploy.validation.ecr.repo.loading=Still loading the list of ECR Repos...
serverless.application.deploy.validation.new.stack.name.duplicate=New stack name must be different than the existing stack name(s)
serverless.application.deploy.validation.new.stack.name.invalid=A stack name can contain only alphanumeric characters (case-sensitive) and hyphens. It must start with an alphabetic character.
serverless.application.deploy.validation.new.stack.name.missing=Enter the name of your new Stack
serverless.application.deploy.validation.new.stack.name.too.long=Stack names must not exceed {0} characters in length
serverless.application.deploy.validation.s3.bucket.empty=Select an S3 Bucket
serverless.application.deploy.validation.s3.bucket.loading=Still loading the list of S3 buckets...
serverless.application.deploy.validation.stack.loading=Still loading CloudFormation stacks...
serverless.application.deploy.validation.stack.missing=Specify a Stack to deploy to
serverless.application.deploy.validation.template.values.badRegex=AllowedPattern for {0} is not valid: {1}
serverless.application.deploy.validation.template.values.failsRegex=Template value for {0} does not match AllowedPattern defined in template: {1}
serverless.application.deploy.validation.template.values.notANumber=Template value for {0} is not a number: {1}
serverless.application.deploy.validation.template.values.tooBig=Template value for {0} is larger than MaxValue defined in template: {1}
serverless.application.deploy.validation.template.values.tooLong=Template value for {0} exceeds MaxLength defined in template: {1}
serverless.application.deploy.validation.template.values.tooShort=Template value for {0} does not meet MinLength defined in template: {1}
serverless.application.deploy.validation.template.values.tooSmall=Template value for {0} is smaller than MinValue defined in template: {1}
serverless.application.deploy_in_progress.title=Deploying Application {0}
serverless.application.sync=Sync Serverless Application (formerly Deploy)
serverless.application.sync.action.description=Sync Serverless Application with cloud
serverless.application.sync.action.name=Sync
serverless.application.sync.code=Sync Serverless Application (code only)
serverless.application.sync.confirm.dev.stack.title=Confirm development stack
serverless.application.sync.dev.mode.warning.text=<html>The SAM CLI will use the AWS Lambda, Amazon API Gateway, and AWS StepFunctions APIs to upload your code without <br> performing a CloudFormation deployment. This will cause drift in your CloudFormation stack.<p> <b>The sync command should only be used against a development stack</b>.</p><p> Confirm that you are synchronizing a development stack.</p></html>
serverless.application.sync.error.bad_parse=Error parsing SAM template {0}\n\n {1}
serverless.application.sync.error.no_resources=Cannot find any resources in SAM template {0}
serverless.application.sync.error.unsupported_runtime_group=The runtime {0} is not supported for syncing the SAM template {1}
serverless.application.sync.execution_failed=SAM sync did not complete successfully
serverless.application.sync.fetch.stacks.progress.bar=Fetching stacks
serverless.application.sync.label.bucket=S3 Bucket:
serverless.application.sync.label.repo=ECR Repository:
serverless.application.sync.label.stack.new=Create Stack:
serverless.application.sync.label.stack.select=Update Stack:
serverless.application.sync.template.parameters=Template Parameters
serverless.application.sync.toast.template_file_failure=Could not detect template file
serverless.application.sync.tooltip.createStack=The name of the AWS CloudFormation stack that is created when syncing this Serverless application.
serverless.application.sync.tooltip.ecrRepo=An Amazon Elastic Container Registry Repository in the same AWS Region to deploy the Lambda images to.
serverless.application.sync.tooltip.s3Bucket=An Amazon S3 bucket in the same AWS Region to sync this serverless application.
serverless.application.sync.tooltip.template.parameters=The parameters that the AWS Toolkit detected in the project's AWS SAM template file for this Serverless application.
serverless.application.sync.tooltip.updateStack=The name of the existing AWS CloudFormation stack that is used when syncing this Serverless application.
serverless.application.sync.use_container=Build function inside a container
serverless.application.sync.validation.ecr.repo.empty=Select an ECR Repository
serverless.application.sync.validation.ecr.repo.loading=Still loading the list of ECR Repos...
serverless.application.sync.validation.new.stack.name.duplicate=New stack name must be different than the existing stack name(s)
serverless.application.sync.validation.new.stack.name.invalid=A stack name can contain only alphanumeric characters (case-sensitive) and hyphens. It must start with an alphabetic character.
serverless.application.sync.validation.new.stack.name.missing=Enter the name of your new Stack
serverless.application.sync.validation.new.stack.name.too.long=Stack names must not exceed {0} characters in length
serverless.application.sync.validation.s3.bucket.empty=Select an S3 Bucket
serverless.application.sync.validation.s3.bucket.loading=Still loading the list of S3 buckets...
serverless.application.sync.validation.stack.loading=Still loading CloudFormation stacks...
serverless.application.sync.validation.stack.missing=Specify a Stack to sync to
session_manager_plugin_installation_warning=<html>ECS Exec makes use of AWS Systems Manager (SSM) Session Manager to establish a connection with the running container. <br/>Please <a href = "https://docs.aws.amazon.com/systems-manager/latest/userguide/session-manager-working-with-install-plugin.html">Install the Session Manager Plugin</a> before proceeding!</html>
session_manager_plugin_installation_warning_title=Install Session Manager Plugin
settings.credentials=IAM Credentials (Locally Configured)
settings.credentials.get_started=Connect to AWS to get started
settings.credentials.iam=IAM Credentials
settings.credentials.iam.add=Add IAM credentials to view resources
settings.credentials.iam.none_selected=No IAM credentials linked
settings.credentials.iam.select=Select from list above to view resources
settings.credentials.iam_and_regions=IAM Credentials & Regions
settings.credentials.individual_identity_sub_menu=Individual Identity
settings.credentials.none_selected=No credentials selected
settings.credentials.profile_sub_menu=All Local Credentials
settings.credentials.prompt_for_default_region_switch=Change region to default region: {0}?
settings.credentials.prompt_for_default_region_switch.always=Always
settings.credentials.prompt_for_default_region_switch.always.description=Always select default region when a credential is selected
settings.credentials.prompt_for_default_region_switch.ask.description=Ask before changing regions when a credential is selected
settings.credentials.prompt_for_default_region_switch.never=Never
settings.credentials.prompt_for_default_region_switch.never.description=Never change region when a credential is selected
settings.credentials.prompt_for_default_region_switch.setting_label=Default region handling:
settings.credentials.prompt_for_default_region_switch.yes=Yes
settings.credentials.recent=Recent IAM Credentials (Locally Configured)
settings.never_show_again=Never Show Again
settings.none_selected=No region or credentials selected
settings.partitions=Other Partitions
settings.profiles.always=Always show when profiles are loaded
settings.profiles.label=AWS profiles notification:
settings.profiles.never=Never show
settings.profiles.on_failure=Show when one or more profiles fail to load
settings.refresh.description=Refresh AWS Connection
settings.regions.none_selected=No region selected
settings.regions.recent=Recent Regions
settings.regions.region_sub_menu=All Regions
settings.retry=Retry
settings.states.initializing=Toolkit initializing...
settings.states.invalid=Unable to connect to AWS:\n{0}
settings.states.invalid.short=Unable to connect
settings.states.validating=Validating connection to AWS...
settings.states.validating.short=Validating connection
settings.statusbar.widget.connections.n={0} Connections
settings.statusbar.widget.expired.1={0} Expired
settings.statusbar.widget.expired.n={0} Connections Expired
settings.statusbar.widget.format=AWS: {0}
settings.title=AWS Connection Settings
sqs.configure.lambda=Configure Lambda Trigger...
sqs.configure.lambda.configure.tooltip=It may take a few seconds to configure.
sqs.configure.lambda.error=Failed to add trigger for function {0}. Try again later.
sqs.configure.lambda.function=Lambda Function:
sqs.configure.lambda.in_progress=Configuring...
sqs.configure.lambda.select=Select Lambda Function:
sqs.configure.lambda.success=Trigger successfully added to function {0}
sqs.configure.lambda.tooltip=<html><p>Configure the queue to trigger an AWS Lambda function when new messages arrive in the queue.</p><p>The queue and Lambda function must be in the same AWS Region.</p></html>
sqs.configure.lambda.validation.function=Lambda function must be specified.
sqs.confirm.iam=Create IAM Role
sqs.confirm.iam.create=Add Policy
sqs.confirm.iam.failed=Failed to create and attach role policy.
sqs.confirm.iam.in_progress=Creating...
sqs.confirm.iam.warning.sqs_queue_permissions=<html>The selected Queue does not have permission to receive messages from the selected SNS Topic. <p>The following policy statement will be added to the queue policy:</p></html>
sqs.confirm.iam.warning.text=<html>The selected Lambda function does not have permission to access SQS.<p>The following IAM role policy will be added to the function's execution role:</p></html>
sqs.copy.message=Copy {0, choice, 1#message|2#{0,number} messages}
sqs.create.fifo.label=FIFO
sqs.create.queue=Create Queue...
sqs.create.queue.create=Create
sqs.create.queue.failed=Failed to create queue {0}
sqs.create.queue.name.label=Queue Name:
sqs.create.queue.title=Create Queue
sqs.create.queue.tooltip=It may take up to 60 seconds for the queue to be visible in the AWS Explorer.
sqs.create.queue.type.label=Queue Type:
sqs.create.standard.label=Standard
sqs.create.validation.empty.queue.name=Queue name must be specified.
sqs.create.validation.long.queue.name=Queue name must not exceed {0} characters in length.
sqs.create.validation.queue.name.invalid=Queue names can only contain alphanumeric characters, hyphens (-), and underscores (_).
sqs.delete.message.action=Delete {0, choice, 1#message|2#{0,number} messages}
sqs.delete.message.failed=Failed to delete {0, choice, 1#message|2#{0,number} messages}.
sqs.delete.message.partial_successs=Failed to delete some messages! {0, choice, 1#message|2#{0,number} messages} were deleted.
sqs.delete.message.succeeded=Successfully deleted {0, choice, 1#1 message|2#{0,number} messages}.
sqs.delete.queue.action=Delete Queue...
sqs.edit.attributes=Edit Queue Parameters
sqs.edit.attributes.action=Edit Queue Parameters...
sqs.edit.attributes.delivery_delay=Delivery Delay (seconds):
sqs.edit.attributes.delivery_delay.tooltip=The amount of time to delay the first delivery of each message.
sqs.edit.attributes.failed=Failed to get queue parameters for queue {0}
sqs.edit.attributes.message_size=Maximum Message Size (bytes):
sqs.edit.attributes.message_size.tooltip=How many bytes a message can contain before it is rejected. Must be between {0} and {1} bytes.
sqs.edit.attributes.queue.attributes=Queue Parameters
sqs.edit.attributes.retention_period=Message Retention Period (seconds):
sqs.edit.attributes.retention_period.tooltip=The amount of time that a message is retained in the queue. Must be between {0} and {1} seconds.
sqs.edit.attributes.retrieving_from_service=Retrieving queue parameters from SQS
sqs.edit.attributes.save=Save
sqs.edit.attributes.updated=Updated queue parameters for {0}.
sqs.edit.attributes.visibility_timeout=Visibility Timeout (seconds):
sqs.edit.attributes.visibility_timeout.tooltip=The length of time that a received message will not be visible to other message consumers.
sqs.edit.attributes.wait_time=Receive Message Wait Time (seconds):
sqs.edit.attributes.wait_time.tooltip=The amount of time that polling will wait for messages to become available.
sqs.failed_to_load_total=Failed to load number of messages available
sqs.failed_to_poll_messages=Failed to view messages
sqs.failed_to_send_message=Failed to send message
sqs.fifo.queue.tooltip=Supports first-in-first-out delivery and message ordering is preserved.
sqs.message.deduplication_id.tooltip=<html><p>Used for deduplication of sent messages. </p><p>If a message with a particular message deduplication ID is sent successfully, any messages sent with the same message deduplication ID are accepted successfully but aren't delivered during the 5-minute deduplication interval.</p></html>
sqs.message.group_id.tooltip=<html><p>Specifies that a message belongs to a specific message group. </p><p>Messages that belong to the same message group are always processed in a strict order relative to the message group.</p></html>
sqs.message.message_body=Message Body
sqs.message.message_id=Message ID
sqs.message.no_messages=No messages retrieved
sqs.message.sender_id=Sender ID
sqs.message.timestamp=Sent Timestamp
sqs.message.validation.empty.deduplication_id=Enter a deduplication ID.
sqs.message.validation.empty.group_id=Enter a group ID.
sqs.message.validation.empty.message.body=Enter a message body
sqs.message.validation.long.id=ID exceeds the maximum length of 128 characters.
sqs.message_table_initial_text=Press "{0}" to load messages
sqs.messages.available.text={0, choice, 0#No messages|1#1 message|2#{0,number} messages} available
sqs.poll.message=View Messages
sqs.poll.warning.text=<html><p>Messages are returned to the queue immediately upon viewing.</p></html>
sqs.purge_queue=Purge Queue
sqs.purge_queue.action=Purge Queue...
sqs.purge_queue.confirm=Are you sure you want to purge queue {0} (approximately {1, choice, 1#1 message|2#{1,number} messages})?
sqs.purge_queue.confirm.title=Purge Queue?
sqs.purge_queue.failed=Failed to purge queue {0}
sqs.purge_queue.failed.60_seconds=Purge queue request already in progress for queue {0}
sqs.purge_queue.succeeded=Started purging queue {0}
sqs.queue.name.tooltip=<html><p>A queue name is case-sensitive and can have up to 80 characters of alphanumeric characters, hyphens (-), and underscores ( _ ). </p><p>If FIFO is selected, '.fifo' will be appended to the specified name.</p></html>
sqs.queue.polled.messages=Polled Messages
sqs.required.empty.text=(Required)
sqs.send.message=Send a Message
sqs.send.message.body.empty.text=Enter message body
sqs.send.message.clear.button=Clear
sqs.send.message.deduplication_id=Deduplication ID:
sqs.send.message.group_id=Group ID:
sqs.send.message.send.button=Send
sqs.send.message.success=Sent message ID: {0}
sqs.service_name=Amazon SQS
sqs.standard.queue.tooltip=Supports at-least-once delivery and message ordering is not preserved.
sqs.subscribe.sns=Subscribe to SNS topic...
sqs.subscribe.sns.failed=Failed to subscribe {0} to {1}
sqs.subscribe.sns.in_progress=Subscribing...
sqs.subscribe.sns.select=Select SNS topic
sqs.subscribe.sns.select.tooltip=Messages from the selected topic are sent to the queue.
sqs.subscribe.sns.subscribe=Subscribe
sqs.subscribe.sns.success=Subscribed successfully to topic {0}.
sqs.subscribe.sns.topic=SNS topic:
sqs.subscribe.sns.validation.empty_topic=Topic must be specified.
sqs.toolwindow=SQS
sqs.url.parse_error=Error parsing SQS queue URL
tags.title=Tags
testgen.button.feedback=How can we make /test better?
testgen.error.generic_error_message=Amazon Q encountered an error while generating tests. Try again later.
testgen.error.generic_technical_error_message=I am experiencing technical difficulties at the moment. Please try again in a few minutes.
testgen.error.maximum_generations_reach=You've reached the monthly quota for Amazon Q Developer's agent capabilities. You can try again next month. For more information on usage limits, see the <a href=\"https://aws.amazon.com/q/developer/pricing/\" target=\"_blank\">Amazon Q Developer pricing page</a>.
testgen.message.cancelled=Unit test generation cancelled.
testgen.message.failed=Test generation failed
testgen.message.regenerate_input=Sure thing. Please provide new instructions for me to generate the tests, and select the function(s) you would like to test.
testgen.message.success=Unit test generation completed.
testgen.no_file_found=Sorry, there isn't a source file open right now that I can generate a test for. Make sure you open a source file so I can generate tests.
testgen.placeholder.enter_slash_quick_actions=Enter "/" for quick actions
testgen.placeholder.newtab=Ask any coding question or type \u0022/\u0022 for actions
testgen.placeholder.select_an_option = Please select an action to proceed (Accept or Reject)
testgen.placeholder.view_diff=Select View Diff to see the generated unit tests
testgen.placeholder.waiting_on_your_inputs=Waiting on your inputs...
testgen.progressbar.generate_unit_tests=Generating unit tests...
toolkit.login.aws_builder_id.already_connected.cancel=Use existing AWS Builder ID
toolkit.login.aws_builder_id.already_connected.message=You already signed in with an AWS Builder ID.\nSign out to add another?
toolkit.login.aws_builder_id.already_connected.reconnect=Sign out
toolkit.login.aws_builder_id.already_connected.title=Sign out of current AWS Builder ID?
toolkit.login.dialog.aws_builder_id.comment=AWS Builder ID is a new personal profile for builders. <a href='https://docs.aws.amazon.com/toolkit-for-jetbrains/latest/userguide/builder-id.html'>Learn More</a>
toolkit.login.dialog.aws_builder_id.title=Use a personal email to sign up and sign in with AWS Builder ID
toolkit.login.dialog.connect_button=Connect
toolkit.login.dialog.connect_inprogress=Waiting to Authenticate...
toolkit.login.dialog.iam.comment=<a>Edit AWS credentials file(s)</a>
toolkit.login.dialog.iam.text_field.access_key_id=Access Key ID:
toolkit.login.dialog.iam.text_field.secret_access_key=Secret Access Key:
toolkit.login.dialog.iam.title=Use IAM Credentials
toolkit.login.dialog.label=Select a connection option
toolkit.login.dialog.sso.comment=Sign in to your company's IAM Identity Center access portal login page.
toolkit.login.dialog.sso.text_field.region=Region:
toolkit.login.dialog.sso.text_field.start_url=Start URL:
toolkit.login.dialog.sso.title=Connect using AWS IAM Identity Center
toolkit.login.dialog.title=AWS Toolkit: Add Connection
toolkit.login.singleton=Only one browser authorization flow may be active at once
toolkit.sso_expire.dialog.cancel_button=Cancel
toolkit.sso_expire.dialog.no_button=Don't show again
toolkit.sso_expire.dialog.title=Connection Expired
toolkit.sso_expire.dialog.yes_button=Re-authenticate
toolkit.sso_expire.dialog_message=Your Amazon Q connection has expired. Please re-authenticate.
toolwindow.stripe.aws.codewhisperer.codereference=Code Reference Log
toolwindow.stripe.aws.codewhisperer.codetransform=Transformation Hub
