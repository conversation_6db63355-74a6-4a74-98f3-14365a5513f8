version: 0.2

cache:
  paths:
#    - '/root/.gradle/caches/**/*'
    - '/root/.gradle/wrapper/**/*'

env:
  variables:
    CI: true
    LOCAL_ENV_RUN: true
    AWS_STS_REGIONAL_ENDPOINTS: regional

phases:
  install:
    commands:
      - startDocker.sh
      # should probably be managed as an extension/rule in any tests that need a screen available
      - /usr/bin/Xvfb :22 -screen 0 1920x1080x24 &

  build:
    commands:
      - |
        if [ "$CODEARTIFACT_DOMAIN_NAME" ] && [ "$CODEARTIFACT_REPO_NAME" ]; then
          CODEARTIFACT_URL=$(aws codeartifact get-repository-endpoint --domain $CODEARTIFACT_DOMAIN_NAME --repository $CODEARTIFACT_REPO_NAME --format maven --query repositoryEndpoint --output text)
          # CODEARTIFACT_NUGET_URL=$(aws codeartifact get-repository-endpoint --domain $CODEARTIFACT_DOMAIN_NAME --repository $CODEARTIFACT_REPO_NAME --format nuget --query repositoryEndpoint --output text)
          CODEARTIFACT_AUTH_TOKEN=$(aws codeartifact get-authorization-token --domain $CODEARTIFACT_DOMAIN_NAME --query authorizationToken --output text --duration-seconds 3600)
        fi

      - AWS_CONFIG_FILE=`mktemp`
      - |
        >$AWS_CONFIG_FILE echo "[default]
        role_arn=$ASSUME_ROLE_ARN
        credential_source=EcsContainer"
      - ls -alh $AWS_CONFIG_FILE
      - cat $AWS_CONFIG_FILE
      - chmod +x gradlew
      - DISPLAY=:22 ./gradlew -PideProfileName=$ALTERNATIVE_IDE_PROFILE_NAME integrationTest coverageReport -x :plugin-toolkit:jetbrains-rider:integrationTest --info --console plain
      - |
        if [ $(docker ps -q | wc -l) -gt 0 ]; then
            echo 'Docker containers were not completely cleaned up!';
            docker ps;
            for container in $(docker ps -q); do
                echo $container;
                docker exec -i $container sh -c 'tail -n +1 /tmp/logs/*';
            done;
            
            exit 1;
        fi
      - VCS_COMMIT_ID="${CODEBUILD_RESOLVED_SOURCE_VERSION}"
      - CI_BUILD_URL=$(echo $CODEBUILD_BUILD_URL | sed 's/#/%23/g') # Encode `#` in the URL because otherwise the url is clipped in the Codecov.io site
      - CI_BUILD_ID="${CODEBUILD_BUILD_ID}"
      - test -n "$CODE_COV_TOKEN" && curl -Os https://uploader.codecov.io/latest/linux/codecov && chmod +x codecov || true # this sometimes times out but we don't want to fail the build
      - test -n "$CODE_COV_TOKEN" && test -n "$CODEBUILD_BUILD_SUCCEEDING" && ./codecov -t $CODE_COV_TOKEN -F integtest || true

  post_build:
    commands:
      - TEST_ARTIFACTS="/tmp/testArtifacts"
      - mkdir -p $TEST_ARTIFACTS/test-reports
      - rsync -rmq --include='*/' --include '**/build/idea-sandbox/system*/log/**' --exclude='*' . $TEST_ARTIFACTS/ || true
      - rsync -rmq --include='*/' --include '**/build/reports/**' --exclude='*' . $TEST_ARTIFACTS/ || true
      - rsync -rmq --include='*/' --include '**/test-results/**/*.xml' --exclude='*' . $TEST_ARTIFACTS/test-reports || true

reports:
  integ-test:
    files:
      - "**/*"
    base-directory: /tmp/testArtifacts/test-reports
    discard-paths: yes

artifacts:
  files:
    - "**/*"
  base-directory: /tmp/testArtifacts
