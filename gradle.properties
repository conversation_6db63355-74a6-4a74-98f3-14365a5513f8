# Copyright 2019 Amazon.com, Inc. or its affiliates. All Rights Reserved.
# SPDX-License-Identifier: Apache-2.0

# Toolkit Version
toolkitVersion=3.74-SNAPSHOT

# Publish Settings
publishToken=
publishChannel=

ideProfileName=2025.1

remoteRobotPort=8080

# Code style
kotlin.code.style=official

# Gradle settings
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.jvmargs=-Xmx4g
kotlin.daemon.jvmargs=-Xmx1500m -Xms500m

# prefer non-enterprise variant of test-retry
systemProp.develocity.testretry.enabled=false

# outputs to build/reports/kotlin-build
kotlin.build.report.output=file

# don't bundle Kotlin stdlib with plugin
kotlin.stdlib.default.dependency=false
